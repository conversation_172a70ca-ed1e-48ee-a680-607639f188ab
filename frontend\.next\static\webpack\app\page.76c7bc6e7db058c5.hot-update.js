"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MealMatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./contexts/CartContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        y: 20,\n        opacity: 0\n    },\n    visible: {\n        y: 0,\n        opacity: 1,\n        transition: {\n            type: \"spring\",\n            stiffness: 100,\n            damping: 10\n        }\n    }\n};\nconst floatingVariants = {\n    animate: {\n        y: [\n            -10,\n            10,\n            -10\n        ],\n        transition: {\n            duration: 3,\n            repeat: Number.POSITIVE_INFINITY,\n            ease: \"easeInOut\"\n        }\n    }\n};\nconst stats = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"Home Chefs\",\n        value: \"500+\",\n        color: \"text-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"Happy Customers\",\n        value: \"10K+\",\n        color: \"text-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"Orders Delivered\",\n        value: \"50K+\",\n        color: \"text-green-500\"\n    }\n];\nfunction MealMatePage() {\n    _s();\n    const { addToCart } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [likedMeals, setLikedMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meals, setMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    const categories = [\n        \"all\",\n        \"breakfast\",\n        \"lunch\",\n        \"dinner\",\n        \"snacks\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFeaturedMeals();\n    }, []);\n    const fetchFeaturedMeals = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/meals\"));\n            if (response.ok) {\n                const data = await response.json();\n                // Get first 6 available meals for featured section\n                const availableMeals = data.data.filter((meal)=>meal.availability).slice(0, 6);\n                setMeals(availableMeals);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch meals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleLike = (mealId)=>{\n        setLikedMeals((prev)=>prev.includes(mealId) ? prev.filter((id)=>id !== mealId) : [\n                ...prev,\n                mealId\n            ]);\n    };\n    const handleAddToCart = (meal)=>{\n        addToCart({\n            _id: meal._id,\n            name: meal.name,\n            price: meal.price,\n            category: meal.category,\n            provider: meal.provider\n        });\n    };\n    const filteredMeals = meals.filter((meal)=>{\n        const matchesSearch = meal.name.toLowerCase().includes(searchQuery.toLowerCase()) || meal.description.toLowerCase().includes(searchQuery.toLowerCase()) || meal.provider.name.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || meal.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 py-20 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            variants: containerVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-white/20 text-white border-white/30 mb-4\",\n                                            children: \"\\uD83C\\uDF7D️ Homemade with Love\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-orange-100 bg-clip-text text-transparent\",\n                                            children: [\n                                                \"Discover Authentic\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300\",\n                                                    children: \"Home Cooking\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                    variants: itemVariants,\n                                    className: \"text-xl md:text-2xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                    children: \"Connect with local home chefs and enjoy fresh, homemade meals delivered to your doorstep\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-white text-orange-600 hover:bg-orange-50 transform hover:scale-105 transition-all duration-200 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Order Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"border-white text-orange-600 hover:bg-orange-50 hover:text-orange-600 transform hover:scale-105 transition-all duration-200\",\n                                            children: [\n                                                \"Become a Chef\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 right-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        transition: {\n                                            delay: 1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-10 h-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-16 bg-white\",\n                initial: {\n                    opacity: 0,\n                    y: 50\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4 \".concat(stat.color),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-8 h-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-12 bg-gray-50\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"max-w-4xl mx-auto\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900\",\n                                children: \"Find Your Perfect Meal\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search for meals, cuisines, or chefs...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-200\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"flex flex-wrap gap-3 justify-center\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: selectedCategory === category ? \"default\" : \"outline\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"capitalize rounded-full transition-all duration-200 \".concat(selectedCategory === category ? \"bg-orange-500 hover:bg-orange-600 transform scale-105\" : \"hover:bg-orange-50 hover:border-orange-300\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-16\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                variants: containerVariants,\n                                initial: \"hidden\",\n                                animate: \"visible\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-full flex justify-center py-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, this) : filteredMeals.map((meal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: itemVariants,\n                                        whileHover: {\n                                            y: -10,\n                                            scale: 1.02\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300,\n                                            damping: 20\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white h-full flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-6xl\",\n                                                                children: \"\\uD83C\\uDF7D️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                            className: \"absolute top-4 right-4 p-2 rounded-full bg-white/90 backdrop-blur-sm shadow-lg\",\n                                                            onClick: ()=>toggleLike(meal._id),\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.9\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5 transition-colors duration-200 \".concat(likedMeals.includes(meal._id) ? \"text-red-500 fill-red-500\" : \"text-gray-600\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            className: \"absolute top-4 left-4 bg-orange-500 text-white capitalize\",\n                                                            children: meal.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6 flex-1 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 line-clamp-1 flex-1 mr-2\",\n                                                                    children: meal.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 whitespace-nowrap\",\n                                                                    children: [\n                                                                        \"₹\",\n                                                                        meal.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-2 flex-1\",\n                                                            children: meal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"by \",\n                                                                            meal.provider.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: ()=>handleAddToCart(meal),\n                                                                        className: \"bg-orange-500 hover:bg-orange-600 text-white rounded-full px-6 w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Add to Cart\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, meal._id, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, this))\n                            }, selectedCategory + searchQuery, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        !loading && filteredMeals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"text-center py-16\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"No meals found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Try adjusting your search or filters\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-20 bg-gradient-to-r from-orange-500 to-red-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-5xl font-bold mb-6\",\n                                children: \"Ready to Start Cooking?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                children: \"Join our community of home chefs and start earning by sharing your delicious homemade meals\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-white text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg rounded-full shadow-lg\",\n                                    children: [\n                                        \"Become a Chef Partner\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(MealMatePage, \"76bbDxunDAuOi11yoX9H+/I5UBk=\", false, function() {\n    return [\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart\n    ];\n});\n_c = MealMatePage;\nvar _c;\n$RefreshReg$(_c, \"MealMatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});