"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        // Optimistically update the UI first\n        setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                    ...order,\n                    status: newStatus\n                } : order));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                const data = await response.json();\n                // Update with the actual response data\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            ...data.data\n                        } : order));\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n            } else {\n                // Revert the optimistic update on error\n                fetchOrders();\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            // Revert the optimistic update on error\n            fetchOrders();\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        // Optimistically update the UI first\n        setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                    ...order,\n                    status: \"cancelled\"\n                } : order));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                // Remove the order from the list or mark as cancelled\n                const data = await response.json();\n                if (data.data && Object.keys(data.data).length === 0) {\n                    // Order was deleted, remove from list\n                    setOrders((prevOrders)=>prevOrders.filter((order)=>order._id !== orderId));\n                } else {\n                    // Order was marked as cancelled\n                    setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                                ...order,\n                                status: \"cancelled\"\n                            } : order));\n                }\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n            } else {\n                // Revert the optimistic update on error\n                fetchOrders();\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            // Revert the optimistic update on error\n            fetchOrders();\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const getStatusActions = (order)=>{\n        const actions = [];\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    children: \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    children: \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    children: \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    children: \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Order Management\" : \"My Orders\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Manage orders for your meals\" : \"Track your meal orders\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mb-8\",\n                        children: [\n                            \"all\",\n                            \"pending\",\n                            \"confirmed\",\n                            \"preparing\",\n                            \"ready\",\n                            \"delivered\",\n                            \"cancelled\"\n                        ].map((status)=>{\n                            var _statusConfig_status;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: filter === status ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setFilter(status),\n                                className: \"capitalize\",\n                                children: [\n                                    status === \"all\" ? \"All Orders\" : ((_statusConfig_status = statusConfig[status]) === null || _statusConfig_status === void 0 ? void 0 : _statusConfig_status.text) || status,\n                                    status !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: orders.filter((order)=>order.status === status).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No orders found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: filter === \"all\" ? \"You don't have any orders yet.\" : 'No orders with status \"'.concat(filter, '\".')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 15\n                        }, this) : filteredOrders.map((order, index)=>{\n                            var _statusConfig_order_status;\n                            const StatusIcon = ((_statusConfig_order_status = statusConfig[order.status]) === null || _statusConfig_order_status === void 0 ? void 0 : _statusConfig_order_status.icon) || _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                            const statusInfo = statusConfig[order.status];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-full \".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-lg\",\n                                                                        children: order.meal.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Order #\",\n                                                                            order._id.slice(-8)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"\".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\", \" text-white\"),\n                                                        children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text) || order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Total Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₹\",\n                                                                                order.meal.price * order.quantity\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Quantity\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: order.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Delivery Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: new Date(order.deliveryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Address\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold text-sm\",\n                                                                            children: order.deliveryAddress\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? order.user.name : order.provider.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Order Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: new Date(order.createdAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 25\n                                                }, this),\n                                                order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                            children: \"Special Instructions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: order.specialInstructions\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        getStatusActions(order),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"View Details\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 21\n                                }, this)\n                            }, order._id, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 19\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 327,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"Ee5hu5nRR4F/G/rr3uKxu0BVunI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});