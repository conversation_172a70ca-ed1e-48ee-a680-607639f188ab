"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    var _statusConfig_selectedOrder_status, _statusConfig_selectedOrder_status1, _statusConfig_selectedOrder_status2;\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pending\") // Will be set properly in useEffect\n    ;\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Set initial tab based on user role\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\") {\n            setActiveTab(\"pending\");\n        } else {\n            setActiveTab(\"active\");\n        }\n    }, [\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    // Remove auto-refresh - not needed for this implementation\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                // Update the order in local state immediately\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            status: newStatus\n                        } : order));\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n                // Auto-switch tabs for better UX\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\") {\n                    if (newStatus === \"confirmed\" || newStatus === \"preparing\" || newStatus === \"ready\") {\n                        setActiveTab(\"active\");\n                    } else if (newStatus === \"delivered\" || newStatus === \"cancelled\") {\n                        setActiveTab(\"completed\");\n                    }\n                }\n            // Don't call fetchOrders() to avoid page refresh and tab reset\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                // Update the order status in local state\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            status: \"cancelled\"\n                        } : order));\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n                // Auto-switch to history tab for customers\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\") {\n                    setActiveTab(\"history\");\n                }\n            // Don't call fetchOrders() to avoid page refresh\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const handleViewDetails = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, this);\n    }\n    // Render different interfaces based on user role\n    if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefOrdersInterface, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 359,\n            columnNumber: 12\n        }, this);\n    } else {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrdersInterface, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 361,\n            columnNumber: 12\n        }, this);\n    }\n    // Chef Orders Interface\n    function ChefOrdersInterface() {\n        const pendingOrders = orders.filter((order)=>order.status === \"pending\");\n        const activeOrders = orders.filter((order)=>[\n                \"confirmed\",\n                \"preparing\",\n                \"ready\"\n            ].includes(order.status));\n        const completedOrders = orders.filter((order)=>[\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-8 h-8 text-orange-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Order Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Manage incoming orders and track your business\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Pending Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-yellow-600\",\n                                                            children: pendingOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-8 h-8 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: activeOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: completedOrders.filter((order)=>new Date(order.createdAt).toDateString() === new Date().toDateString()).length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Today's Revenue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: [\n                                                                \"₹\",\n                                                                completedOrders.filter((order)=>new Date(order.createdAt).toDateString() === new Date().toDateString() && order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"pending\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pending (\",\n                                                pendingOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"active\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active (\",\n                                                activeOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"completed\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Completed (\",\n                                                completedOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"pending\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefPendingOrders, {\n                                        orders: pendingOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"active\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefActiveOrders, {\n                                        orders: activeOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"completed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefCompletedOrders, {\n                                        orders: completedOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Orders Interface\n    function CustomerOrdersInterface() {\n        const activeOrders = orders.filter((order)=>![\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        const pastOrders = orders.filter((order)=>[\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-8 h-8 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"My Orders\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Track your meal orders and order history\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: activeOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: orders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Spent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: [\n                                                                \"₹\",\n                                                                orders.filter((order)=>order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"active\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active Orders (\",\n                                                activeOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"history\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Order History (\",\n                                                pastOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"active\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerActiveOrders, {\n                                        orders: activeOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"history\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrderHistory, {\n                                        orders: pastOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 505,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 504,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Pending Orders Component\n    function ChefPendingOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No pending orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"New orders will appear here for your approval\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 608,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 border-l-yellow-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: order.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        order._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"bg-yellow-500 text-white\",\n                                            children: \"New Order\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Customer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Delivery Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm bg-gray-50 p-2 rounded\",\n                                                children: order.deliveryAddress\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, this),\n                                    order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-1\",\n                                                children: \"Special Instructions:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm bg-yellow-50 p-2 rounded border border-yellow-200\",\n                                                children: order.specialInstructions\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                children: updatingOrders.has(order._id) ? \"Accepting...\" : \"Accept Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>updateOrderStatus(order._id, \"cancelled\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"text-red-600 border-red-600 hover:bg-red-50\",\n                                                children: updatingOrders.has(order._id) ? \"Declining...\" : \"Decline\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 619,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Active Orders Component\n    function ChefActiveOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No active orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Accepted orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 716,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 715,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(order.status === \"confirmed\" ? \"border-l-blue-500\" : order.status === \"preparing\" ? \"border-l-purple-500\" : \"border-l-orange-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                order.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Delivery Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Order Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.createdAt).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            order.status === \"confirmed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-purple-600 hover:bg-purple-700\",\n                                                children: updatingOrders.has(order._id) ? \"Starting...\" : \"Start Preparing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 23\n                                            }, this),\n                                            order.status === \"preparing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: updatingOrders.has(order._id) ? \"Marking...\" : \"Mark Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 23\n                                            }, this),\n                                            order.status === \"ready\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                children: updatingOrders.has(order._id) ? \"Delivering...\" : \"Mark Delivered\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 732,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 726,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Completed Orders Component\n    function ChefCompletedOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No completed orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Completed orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 844,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 841,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 840,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(order.status === \"delivered\" ? \"border-l-green-500\" : \"border-l-red-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                order.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 874,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 877,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Earnings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: order.status === \"delivered\" ? \"₹\".concat(order.meal.price * order.quantity) : \"₹0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.createdAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Not rated yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>handleViewDetails(order),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 863,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 857,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 851,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Active Orders Component\n    function CustomerActiveOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 938,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No active orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 939,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Your active orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 937,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 936,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 966,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 962,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Chef\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 993,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Order Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    \"pending\",\n                                                    \"confirmed\",\n                                                    \"preparing\",\n                                                    \"ready\",\n                                                    \"delivered\"\n                                                ].map((status, idx)=>{\n                                                    const isActive = [\n                                                        \"pending\",\n                                                        \"confirmed\",\n                                                        \"preparing\",\n                                                        \"ready\",\n                                                        \"delivered\"\n                                                    ].indexOf(order.status) >= idx;\n                                                    const isCurrent = order.status === status;\n                                                    const isCompleted = [\n                                                        \"pending\",\n                                                        \"confirmed\",\n                                                        \"preparing\",\n                                                        \"ready\",\n                                                        \"delivered\"\n                                                    ].indexOf(order.status) > idx;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full border-2 transition-all duration-300 \".concat(isCompleted ? \"bg-green-500 border-green-500\" : isCurrent ? \"bg-orange-500 border-orange-500 animate-pulse\" : isActive ? \"bg-blue-500 border-blue-500\" : \"bg-gray-200 border-gray-300\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            idx < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-0.5 transition-all duration-300 \".concat(isCompleted || isActive && idx < [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) ? \"bg-green-500\" : \"bg-gray-200\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, status, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1009,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between mt-1\",\n                                                children: [\n                                                    \"Pending\",\n                                                    \"Confirmed\",\n                                                    \"Preparing\",\n                                                    \"Ready\",\n                                                    \"Delivered\"\n                                                ].map((label, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs transition-all duration-300 \".concat([\n                                                            \"pending\",\n                                                            \"confirmed\",\n                                                            \"preparing\",\n                                                            \"ready\",\n                                                            \"delivered\"\n                                                        ].indexOf(order.status) >= idx ? \"text-gray-700 font-medium\" : \"text-gray-400\"),\n                                                        children: label\n                                                    }, label, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            [\n                                                \"pending\",\n                                                \"confirmed\"\n                                            ].includes(order.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"sm\",\n                                                onClick: ()=>cancelOrder(order._id),\n                                                disabled: updatingOrders.has(order._id),\n                                                children: updatingOrders.has(order._id) ? \"Cancelling...\" : \"Cancel Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 953,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 947,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Order History Component\n    function CustomerOrderHistory(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1093,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No order history\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Your completed orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1095,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1092,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1091,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"opacity-90 hover:opacity-100 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1118,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                new Date(order.createdAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1123,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1117,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1116,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Chef\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1139,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1140,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1136,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1146,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1145,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1143,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Paid\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1154,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1150,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Delivery Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1160,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1159,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1157,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            order.status === \"delivered\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-yellow-600 border-yellow-600 hover:bg-yellow-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Rate Order\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1182,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1177,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1166,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1134,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 1114,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1108,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 1102,\n            columnNumber: 7\n        }, this);\n    }\n    // Main render - choose interface based on user role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefOrdersInterface, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1198,\n                columnNumber: 36\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrdersInterface, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1198,\n                columnNumber: 62\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: showOrderDetails,\n                onOpenChange: setShowOrderDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                    className: \"max-w-2xl max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                children: \"Order Details\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1203,\n                            columnNumber: 11\n                        }, this),\n                        selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: selectedOrder.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        selectedOrder._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(((_statusConfig_selectedOrder_status = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status === void 0 ? void 0 : _statusConfig_selectedOrder_status.color) || \"bg-gray-500\", \" text-white\"),\n                                            children: ((_statusConfig_selectedOrder_status1 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status1 === void 0 ? void 0 : _statusConfig_selectedOrder_status1.text) || selectedOrder.status\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1210,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1224,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? selectedOrder.user.name : selectedOrder.provider.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1227,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Quantity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedOrder.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1234,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: [\n                                                                \"₹\",\n                                                                selectedOrder.meal.price * selectedOrder.quantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Delivery Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.deliveryDate).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1246,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Order Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1251,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1255,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (_statusConfig_selectedOrder_status2 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status2 === void 0 ? void 0 : _statusConfig_selectedOrder_status2.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Delivery Address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.deliveryAddress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1262,\n                                    columnNumber: 15\n                                }, this),\n                                selectedOrder.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Special Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.specialInstructions\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1269,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 pt-4 border-t\",\n                                    children: [\n                                        getStatusActions(selectedOrder),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowOrderDetails(false),\n                                            className: \"ml-auto\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1276,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1208,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(OrdersPage, \"1seJ7u09xFZY7OFi/3OyQZLslxU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});