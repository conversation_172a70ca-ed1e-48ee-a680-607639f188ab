"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MealMatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./contexts/CartContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        y: 20,\n        opacity: 0\n    },\n    visible: {\n        y: 0,\n        opacity: 1,\n        transition: {\n            type: \"spring\",\n            stiffness: 100,\n            damping: 10\n        }\n    }\n};\nconst floatingVariants = {\n    animate: {\n        y: [\n            -10,\n            10,\n            -10\n        ],\n        transition: {\n            duration: 3,\n            repeat: Number.POSITIVE_INFINITY,\n            ease: \"easeInOut\"\n        }\n    }\n};\nconst stats = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"Home Chefs\",\n        value: \"500+\",\n        color: \"text-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"Happy Customers\",\n        value: \"10K+\",\n        color: \"text-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"Orders Delivered\",\n        value: \"50K+\",\n        color: \"text-green-500\"\n    }\n];\nfunction MealMatePage() {\n    _s();\n    const { addToCart } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [likedMeals, setLikedMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meals, setMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    const categories = [\n        \"all\",\n        \"breakfast\",\n        \"lunch\",\n        \"dinner\",\n        \"snacks\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFeaturedMeals();\n    }, []);\n    const fetchFeaturedMeals = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/meals\"));\n            if (response.ok) {\n                const data = await response.json();\n                // Get first 6 available meals for featured section\n                const availableMeals = data.data.filter((meal)=>meal.availability).slice(0, 6);\n                setMeals(availableMeals);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch meals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleLike = (mealId)=>{\n        setLikedMeals((prev)=>prev.includes(mealId) ? prev.filter((id)=>id !== mealId) : [\n                ...prev,\n                mealId\n            ]);\n    };\n    const handleAddToCart = (meal)=>{\n        addToCart({\n            _id: meal._id,\n            name: meal.name,\n            price: meal.price,\n            category: meal.category,\n            provider: meal.provider\n        });\n    };\n    const filteredMeals = meals.filter((meal)=>{\n        const matchesSearch = meal.name.toLowerCase().includes(searchQuery.toLowerCase()) || meal.description.toLowerCase().includes(searchQuery.toLowerCase()) || meal.provider.name.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || meal.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 py-20 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            variants: containerVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-white/20 text-white border-white/30 mb-4\",\n                                            children: \"\\uD83C\\uDF7D️ Homemade with Love\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-orange-100 bg-clip-text text-transparent\",\n                                            children: [\n                                                \"Discover Authentic\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300\",\n                                                    children: \"Home Cooking\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                    variants: itemVariants,\n                                    className: \"text-xl md:text-2xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                    children: \"Connect with local home chefs and enjoy fresh, homemade meals delivered to your doorstep\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-white text-orange-600 hover:bg-orange-50 transform hover:scale-105 transition-all duration-200 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Order Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"border-white text-orange-600 hover:bg-orange-50 hover:text-orange-600 transform hover:scale-105 transition-all duration-200\",\n                                            children: [\n                                                \"Become a Chef\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 right-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        transition: {\n                                            delay: 1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-10 h-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-16 bg-white\",\n                initial: {\n                    opacity: 0,\n                    y: 50\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4 \".concat(stat.color),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-8 h-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-12 bg-gray-50\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"max-w-4xl mx-auto\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900\",\n                                children: \"Find Your Perfect Meal\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search for meals, cuisines, or chefs...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-200\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"flex flex-wrap gap-3 justify-center\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: selectedCategory === category ? \"default\" : \"outline\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"capitalize rounded-full transition-all duration-200 \".concat(selectedCategory === category ? \"bg-orange-500 hover:bg-orange-600 transform scale-105\" : \"hover:bg-orange-50 hover:border-orange-300\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-16\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                variants: containerVariants,\n                                initial: \"hidden\",\n                                animate: \"visible\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-full flex justify-center py-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, this) : filteredMeals.map((meal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: itemVariants,\n                                        whileHover: {\n                                            y: -10,\n                                            scale: 1.02\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300,\n                                            damping: 20\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white h-full flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-6xl\",\n                                                                children: \"\\uD83C\\uDF7D️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                            className: \"absolute top-4 right-4 p-2 rounded-full bg-white/90 backdrop-blur-sm shadow-lg\",\n                                                            onClick: ()=>toggleLike(meal._id),\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.9\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5 transition-colors duration-200 \".concat(likedMeals.includes(meal._id) ? \"text-red-500 fill-red-500\" : \"text-gray-600\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            className: \"absolute top-4 left-4 bg-orange-500 text-white capitalize\",\n                                                            children: meal.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6 flex-1 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 line-clamp-1 flex-1 mr-2\",\n                                                                    children: meal.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 whitespace-nowrap\",\n                                                                    children: [\n                                                                        \"₹\",\n                                                                        meal.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-2 flex-1\",\n                                                            children: meal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"by \",\n                                                                            meal.provider.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: ()=>handleAddToCart(meal),\n                                                                        className: \"bg-orange-500 hover:bg-orange-600 text-white rounded-full px-6 w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Add to Cart\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, meal._id, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 19\n                                    }, this))\n                            }, selectedCategory + searchQuery, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        !loading && filteredMeals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"text-center py-16\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"No meals found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Try adjusting your search or filters\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-20 bg-gradient-to-r from-orange-500 to-red-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-5xl font-bold mb-6\",\n                                children: \"Ready to Start Cooking?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                children: \"Join our community of home chefs and start earning by sharing your delicious homemade meals\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-white text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg rounded-full shadow-lg\",\n                                    children: [\n                                        \"Become a Chef Partner\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: cartItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"fixed bottom-6 right-6 bg-orange-500 text-white p-4 rounded-full shadow-lg z-50\",\n                    initial: {\n                        scale: 0,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0,\n                        opacity: 0\n                    },\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: cartItems.length\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(MealMatePage, \"76bbDxunDAuOi11yoX9H+/I5UBk=\", false, function() {\n    return [\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart\n    ];\n});\n_c = MealMatePage;\nvar _c;\n$RefreshReg$(_c, \"MealMatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});