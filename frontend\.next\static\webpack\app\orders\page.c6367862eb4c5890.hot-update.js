"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    var _statusConfig_selectedOrder_status, _statusConfig_selectedOrder_status1, _statusConfig_selectedOrder_status2;\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"pending\" : \"active\");\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Remove auto-refresh - not needed for this implementation\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n                // Refresh orders to get updated data\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n                // Refresh orders to show updated status\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const handleViewDetails = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, this);\n    }\n    // Render different interfaces based on user role\n    if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefOrdersInterface, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 314,\n            columnNumber: 12\n        }, this);\n    } else {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrdersInterface, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 316,\n            columnNumber: 12\n        }, this);\n    }\n    // Chef Orders Interface\n    function ChefOrdersInterface() {\n        const pendingOrders = orders.filter((order)=>order.status === \"pending\");\n        const activeOrders = orders.filter((order)=>[\n                \"confirmed\",\n                \"preparing\",\n                \"ready\"\n            ].includes(order.status));\n        const completedOrders = orders.filter((order)=>[\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-8 h-8 text-orange-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Order Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Manage incoming orders and track your business\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Pending Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-yellow-600\",\n                                                            children: pendingOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-8 h-8 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: activeOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: completedOrders.filter((order)=>new Date(order.createdAt).toDateString() === new Date().toDateString()).length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Today's Revenue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: [\n                                                                \"₹\",\n                                                                completedOrders.filter((order)=>new Date(order.createdAt).toDateString() === new Date().toDateString() && order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            defaultValue: \"pending\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"pending\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pending (\",\n                                                pendingOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"active\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active (\",\n                                                activeOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"completed\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Completed (\",\n                                                completedOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"pending\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefPendingOrders, {\n                                        orders: pendingOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"active\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefActiveOrders, {\n                                        orders: activeOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"completed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefCompletedOrders, {\n                                        orders: completedOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 326,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Orders Interface\n    function CustomerOrdersInterface() {\n        const activeOrders = orders.filter((order)=>![\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        const pastOrders = orders.filter((order)=>[\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-8 h-8 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"My Orders\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Track your meal orders and order history\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: activeOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: orders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Spent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: [\n                                                                \"₹\",\n                                                                orders.filter((order)=>order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            defaultValue: \"active\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"active\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active Orders (\",\n                                                activeOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"history\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Order History (\",\n                                                pastOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"active\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerActiveOrders, {\n                                        orders: activeOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"history\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrderHistory, {\n                                        orders: pastOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 459,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Pending Orders Component\n    function ChefPendingOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No pending orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"New orders will appear here for your approval\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 563,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 border-l-yellow-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: order.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        order._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"bg-yellow-500 text-white\",\n                                            children: \"New Order\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Customer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Delivery Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm bg-gray-50 p-2 rounded\",\n                                                children: order.deliveryAddress\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 17\n                                    }, this),\n                                    order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-1\",\n                                                children: \"Special Instructions:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm bg-yellow-50 p-2 rounded border border-yellow-200\",\n                                                children: order.specialInstructions\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                children: updatingOrders.has(order._id) ? \"Accepting...\" : \"Accept Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>updateOrderStatus(order._id, \"cancelled\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"text-red-600 border-red-600 hover:bg-red-50\",\n                                                children: updatingOrders.has(order._id) ? \"Declining...\" : \"Decline\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 13\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 574,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Active Orders Component\n    function ChefActiveOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No active orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Accepted orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 670,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(order.status === \"confirmed\" ? \"border-l-blue-500\" : order.status === \"preparing\" ? \"border-l-purple-500\" : \"border-l-orange-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                order.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 698,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Delivery Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Order Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.createdAt).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            order.status === \"confirmed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-purple-600 hover:bg-purple-700\",\n                                                children: updatingOrders.has(order._id) ? \"Starting...\" : \"Start Preparing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 23\n                                            }, this),\n                                            order.status === \"preparing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: updatingOrders.has(order._id) ? \"Marking...\" : \"Mark Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 23\n                                            }, this),\n                                            order.status === \"ready\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                children: updatingOrders.has(order._id) ? \"Delivering...\" : \"Mark Delivered\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 687,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 681,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Completed Orders Component\n    function ChefCompletedOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 797,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No completed orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Completed orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 796,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 795,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(order.status === \"delivered\" ? \"border-l-green-500\" : \"border-l-red-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                order.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Earnings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: order.status === \"delivered\" ? \"₹\".concat(order.meal.price * order.quantity) : \"₹0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.createdAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Not rated yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>handleViewDetails(order),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 812,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 806,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Active Orders Component\n    function CustomerActiveOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No active orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 894,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Your active orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 895,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 892,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 891,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 922,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 921,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Chef\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 937,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Order Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    \"pending\",\n                                                    \"confirmed\",\n                                                    \"preparing\",\n                                                    \"ready\",\n                                                    \"delivered\"\n                                                ].map((status, idx)=>{\n                                                    const isActive = [\n                                                        \"pending\",\n                                                        \"confirmed\",\n                                                        \"preparing\",\n                                                        \"ready\",\n                                                        \"delivered\"\n                                                    ].indexOf(order.status) >= idx;\n                                                    const isCurrent = order.status === status;\n                                                    const isCompleted = [\n                                                        \"pending\",\n                                                        \"confirmed\",\n                                                        \"preparing\",\n                                                        \"ready\",\n                                                        \"delivered\"\n                                                    ].indexOf(order.status) > idx;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full border-2 transition-all duration-300 \".concat(isCompleted ? \"bg-green-500 border-green-500\" : isCurrent ? \"bg-orange-500 border-orange-500 animate-pulse\" : isActive ? \"bg-blue-500 border-blue-500\" : \"bg-gray-200 border-gray-300\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 972,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            idx < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-0.5 transition-all duration-300 \".concat(isCompleted || isActive && idx < [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) ? \"bg-green-500\" : \"bg-gray-200\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, status, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between mt-1\",\n                                                children: [\n                                                    \"Pending\",\n                                                    \"Confirmed\",\n                                                    \"Preparing\",\n                                                    \"Ready\",\n                                                    \"Delivered\"\n                                                ].map((label, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs transition-all duration-300 \".concat([\n                                                            \"pending\",\n                                                            \"confirmed\",\n                                                            \"preparing\",\n                                                            \"ready\",\n                                                            \"delivered\"\n                                                        ].indexOf(order.status) >= idx ? \"text-gray-700 font-medium\" : \"text-gray-400\"),\n                                                        children: label\n                                                    }, label, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 997,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            [\n                                                \"pending\",\n                                                \"confirmed\"\n                                            ].includes(order.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"sm\",\n                                                onClick: ()=>cancelOrder(order._id),\n                                                disabled: updatingOrders.has(order._id),\n                                                children: updatingOrders.has(order._id) ? \"Cancelling...\" : \"Cancel Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 908,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 902,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Order History Component\n    function CustomerOrderHistory(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1048,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No order history\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1049,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Your completed orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1050,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1047,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1046,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"opacity-90 hover:opacity-100 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                new Date(order.createdAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1076,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Chef\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1091,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1101,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Paid\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1105,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Delivery Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1115,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1114,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1090,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            order.status === \"delivered\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-yellow-600 border-yellow-600 hover:bg-yellow-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Rate Order\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1123,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1132,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1121,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 1069,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1063,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 1057,\n            columnNumber: 7\n        }, this);\n    }\n    // Main render - choose interface based on user role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefOrdersInterface, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1153,\n                columnNumber: 36\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrdersInterface, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1153,\n                columnNumber: 62\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: showOrderDetails,\n                onOpenChange: setShowOrderDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                    className: \"max-w-2xl max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                children: \"Order Details\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1158,\n                            columnNumber: 11\n                        }, this),\n                        selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: selectedOrder.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        selectedOrder._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(((_statusConfig_selectedOrder_status = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status === void 0 ? void 0 : _statusConfig_selectedOrder_status.color) || \"bg-gray-500\", \" text-white\"),\n                                            children: ((_statusConfig_selectedOrder_status1 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status1 === void 0 ? void 0 : _statusConfig_selectedOrder_status1.text) || selectedOrder.status\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? selectedOrder.user.name : selectedOrder.provider.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Quantity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedOrder.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1193,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: [\n                                                                \"₹\",\n                                                                selectedOrder.meal.price * selectedOrder.quantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Delivery Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1200,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.deliveryDate).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Order Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1205,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1206,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (_statusConfig_selectedOrder_status2 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status2 === void 0 ? void 0 : _statusConfig_selectedOrder_status2.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1176,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Delivery Address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1218,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.deliveryAddress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1219,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1217,\n                                    columnNumber: 15\n                                }, this),\n                                selectedOrder.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Special Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.specialInstructions\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1226,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1224,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 pt-4 border-t\",\n                                    children: [\n                                        getStatusActions(selectedOrder),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowOrderDetails(false),\n                                            className: \"ml-auto\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(OrdersPage, \"pjVj0jVGmZUF3WODKeNwxsOqrrA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9vcmRlcnMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDTDtBQUN5QztBQUNoQztBQUNGO0FBQzRDO0FBQ1Y7QUFDL0I7QUFDSjtBQW9CdkI7QUEwQnJCLE1BQU1rQyxlQUFlO0lBQ25CQyxTQUFTO1FBQ1BDLE1BQU1qQixxTUFBS0E7UUFDWGtCLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQUMsV0FBVztRQUNUSixNQUFNZCxzTUFBV0E7UUFDakJlLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQUUsV0FBVztRQUNUTCxNQUFNWCxzTUFBT0E7UUFDYlksT0FBTztRQUNQQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBRyxPQUFPO1FBQ0xOLE1BQU1mLHNNQUFPQTtRQUNiZ0IsT0FBTztRQUNQQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBSSxXQUFXO1FBQ1RQLE1BQU1aLHNNQUFLQTtRQUNYYSxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0FLLFdBQVc7UUFDVFIsTUFBTWIsc01BQU9BO1FBQ2JjLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7QUFDRjtBQUVlLFNBQVNNO1FBa2pDYVgsb0NBQ2xCQSxxQ0F3Q3lCQTs7SUExbEMxQyxNQUFNLEVBQUVZLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUc5Qiw4REFBT0E7SUFDL0IsTUFBTSxFQUFFK0IsS0FBSyxFQUFFLEdBQUc5QiwwREFBUUE7SUFDMUIsTUFBTSxDQUFDK0IsUUFBUUMsVUFBVSxHQUFHbEQsK0NBQVFBLENBQVUsRUFBRTtJQUNoRCxNQUFNLENBQUNtRCxTQUFTQyxXQUFXLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNxRCxRQUFRQyxVQUFVLEdBQUd0RCwrQ0FBUUEsQ0FBUztJQUM3QyxNQUFNLENBQUN1RCxnQkFBZ0JDLGtCQUFrQixHQUFHeEQsK0NBQVFBLENBQWMsSUFBSXlEO0lBQ3RFLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUczRCwrQ0FBUUEsQ0FBZTtJQUNqRSxNQUFNLENBQUM0RCxrQkFBa0JDLG9CQUFvQixHQUFHN0QsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDOEQsV0FBV0MsYUFBYSxHQUFHL0QsK0NBQVFBLENBQVM4QyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rQixJQUFJLE1BQUssYUFBYSxZQUFZO0lBRTNGLE1BQU1DLGVBQWVDLDJCQUErQixJQUFJLENBQTJCO0lBRW5GakUsZ0RBQVNBLENBQUM7UUFDUixJQUFJNkMsUUFBUUMsT0FBTztZQUNqQnNCO1FBQ0Y7SUFDRixHQUFHO1FBQUN2QjtRQUFNQztLQUFNO0lBRWhCLDJEQUEyRDtJQUUzRCxNQUFNc0IsY0FBYztRQUNsQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWJOLGNBQWEsWUFBVTtnQkFDckRPLFNBQVM7b0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTjFCO2dCQUMzQjtZQUNGO1lBRUEsSUFBSXVCLFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ2hDMUIsVUFBVXlCLEtBQUtBLElBQUk7WUFDckIsT0FBTztnQkFDTCxNQUFNLElBQUlFLE1BQU07WUFDbEI7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZDlCLE1BQU07Z0JBQ0orQixPQUFPO2dCQUNQeEMsYUFBYTtnQkFDYnlDLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUjVCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTZCLG9CQUFvQixPQUFPQyxTQUFpQkM7UUFDaEQzQixrQkFBa0I0QixDQUFBQSxPQUFRLElBQUkzQixJQUFJMkIsTUFBTUMsR0FBRyxDQUFDSDtRQUU1QyxJQUFJO1lBQ0YsTUFBTVosV0FBVyxNQUFNQyxNQUFNLEdBQTBCVyxPQUF2QmpCLGNBQWEsWUFBa0IsT0FBUmlCLFVBQVc7Z0JBQ2hFSSxRQUFRO2dCQUNSZCxTQUFTO29CQUNQLGdCQUFnQjtvQkFDaEJDLGVBQWUsVUFBZ0IsT0FBTjFCO2dCQUMzQjtnQkFDQXdDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsUUFBUVA7Z0JBQVU7WUFDM0M7WUFFQSxJQUFJYixTQUFTSSxFQUFFLEVBQUU7b0JBRzJCeEM7Z0JBRjFDYyxNQUFNO29CQUNKK0IsT0FBTztvQkFDUHhDLGFBQWEsMkJBQW1HLE9BQXhFTCxFQUFBQSwwQkFBQUEsWUFBWSxDQUFDaUQsVUFBdUMsY0FBcERqRCw4Q0FBQUEsd0JBQXNESSxJQUFJLEtBQUk2QyxXQUFVO2dCQUNsSDtnQkFDQSxxQ0FBcUM7Z0JBQ3JDZDtZQUNGLE9BQU87Z0JBQ0wsTUFBTVMsUUFBUSxNQUFNUixTQUFTTSxJQUFJO2dCQUNqQyxNQUFNLElBQUlDLE1BQU1DLE1BQU1BLEtBQUssSUFBSTtZQUNqQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkOUIsTUFBTTtnQkFDSitCLE9BQU87Z0JBQ1B4QyxhQUFhdUMsaUJBQWlCRCxRQUFRQyxNQUFNYSxPQUFPLEdBQUc7Z0JBQ3REWCxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1J4QixrQkFBa0I0QixDQUFBQTtnQkFDaEIsTUFBTVEsU0FBUyxJQUFJbkMsSUFBSTJCO2dCQUN2QlEsT0FBT0MsTUFBTSxDQUFDWDtnQkFDZCxPQUFPVTtZQUNUO1FBQ0Y7SUFDRjtJQUVBLE1BQU1FLGNBQWMsT0FBT1o7UUFDekIsSUFBSSxDQUFDYSxRQUFRLGdEQUFnRDtZQUMzRDtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU16QixXQUFXLE1BQU1DLE1BQU0sR0FBMEJXLE9BQXZCakIsY0FBYSxZQUFrQixPQUFSaUIsVUFBVztnQkFDaEVJLFFBQVE7Z0JBQ1JkLFNBQVM7b0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTjFCO2dCQUMzQjtZQUNGO1lBRUEsSUFBSXVCLFNBQVNJLEVBQUUsRUFBRTtnQkFDZjFCLE1BQU07b0JBQ0orQixPQUFPO29CQUNQeEMsYUFBYTtnQkFDZjtnQkFDQSx3Q0FBd0M7Z0JBQ3hDOEI7WUFDRixPQUFPO2dCQUNMLE1BQU1TLFFBQVEsTUFBTVIsU0FBU00sSUFBSTtnQkFDakMsTUFBTSxJQUFJQyxNQUFNQyxNQUFNQSxLQUFLLElBQUk7WUFDakM7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZDlCLE1BQU07Z0JBQ0orQixPQUFPO2dCQUNQeEMsYUFBYXVDLGlCQUFpQkQsUUFBUUMsTUFBTWEsT0FBTyxHQUFHO2dCQUN0RFgsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1nQixpQkFBaUIvQyxPQUFPSSxNQUFNLENBQUM0QyxDQUFBQTtRQUNuQyxJQUFJNUMsV0FBVyxPQUFPLE9BQU87UUFDN0IsT0FBTzRDLE1BQU1QLE1BQU0sS0FBS3JDO0lBQzFCO0lBRUEsTUFBTTZDLG9CQUFvQixDQUFDRDtRQUN6QnRDLGlCQUFpQnNDO1FBQ2pCcEMsb0JBQW9CO0lBQ3RCO0lBRUEsTUFBTXNDLG1CQUFtQixDQUFDRjtRQUN4QixNQUFNRyxVQUFVLEVBQUU7UUFDbEIsTUFBTUMsYUFBYTlDLGVBQWUrQyxHQUFHLENBQUNMLE1BQU1NLEdBQUc7UUFFL0MsSUFBSXpELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWtCLElBQUksTUFBSyxjQUFjaUMsTUFBTU8sUUFBUSxDQUFDRCxHQUFHLEtBQUt6RCxLQUFLeUQsR0FBRyxFQUFFO1lBQ2hFLG1CQUFtQjtZQUNuQixJQUFJTixNQUFNUCxNQUFNLEtBQUssV0FBVztnQkFDOUJVLFFBQVFLLElBQUksZUFDViw4REFBQ2xHLHlEQUFNQTtvQkFFTG1HLE1BQUs7b0JBQ0xDLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtvQkFDNUNLLFdBQVU7b0JBQ1ZDLFVBQVVSOzhCQUVUQSxhQUFhLGdCQUFnQjttQkFOMUI7Ozs7O1lBU1Y7WUFDQSxJQUFJSixNQUFNUCxNQUFNLEtBQUssYUFBYTtnQkFDaENVLFFBQVFLLElBQUksZUFDViw4REFBQ2xHLHlEQUFNQTtvQkFFTG1HLE1BQUs7b0JBQ0xDLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtvQkFDNUNLLFdBQVU7b0JBQ1ZDLFVBQVVSOzhCQUVUQSxhQUFhLGdCQUFnQjttQkFOMUI7Ozs7O1lBU1Y7WUFDQSxJQUFJSixNQUFNUCxNQUFNLEtBQUssYUFBYTtnQkFDaENVLFFBQVFLLElBQUksZUFDViw4REFBQ2xHLHlEQUFNQTtvQkFFTG1HLE1BQUs7b0JBQ0xDLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtvQkFDNUNLLFdBQVU7b0JBQ1ZDLFVBQVVSOzhCQUVUQSxhQUFhLGdCQUFnQjttQkFOMUI7Ozs7O1lBU1Y7WUFDQSxJQUFJSixNQUFNUCxNQUFNLEtBQUssU0FBUztnQkFDNUJVLFFBQVFLLElBQUksZUFDViw4REFBQ2xHLHlEQUFNQTtvQkFFTG1HLE1BQUs7b0JBQ0xDLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtvQkFDNUNLLFdBQVU7b0JBQ1ZDLFVBQVVSOzhCQUVUQSxhQUFhLGdCQUFnQjttQkFOMUI7Ozs7O1lBU1Y7UUFDRjtRQUVBLG1CQUFtQjtRQUNuQixJQUFJdkQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNa0IsSUFBSSxNQUFLLGNBQWNpQyxNQUFNbkQsSUFBSSxDQUFDeUQsR0FBRyxLQUFLekQsS0FBS3lELEdBQUcsRUFBRTtZQUM1RCxJQUFJO2dCQUFDO2dCQUFXO2FBQVksQ0FBQ08sUUFBUSxDQUFDYixNQUFNUCxNQUFNLEdBQUc7Z0JBQ25EVSxRQUFRSyxJQUFJLGVBQ1YsOERBQUNsRyx5REFBTUE7b0JBRUxtRyxNQUFLO29CQUNMMUIsU0FBUTtvQkFDUjJCLFNBQVMsSUFBTWIsWUFBWUcsTUFBTU0sR0FBRztvQkFDcENNLFVBQVVSOzhCQUVUQSxhQUFhLGtCQUFrQjttQkFONUI7Ozs7O1lBU1Y7UUFDRjtRQUVBLE9BQU9EO0lBQ1Q7SUFFQSxJQUFJakQsU0FBUztRQUNYLHFCQUNFLDhEQUFDNEQ7WUFBSUgsV0FBVTtzQkFDYiw0RUFBQ0c7Z0JBQUlILFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEsaURBQWlEO0lBQ2pELElBQUk5RCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rQixJQUFJLE1BQUssWUFBWTtRQUM3QixxQkFBTyw4REFBQ2dEOzs7OztJQUNWLE9BQU87UUFDTCxxQkFBTyw4REFBQ0M7Ozs7O0lBQ1Y7SUFFQSx3QkFBd0I7SUFDeEIsU0FBU0Q7UUFDUCxNQUFNRSxnQkFBZ0JqRSxPQUFPSSxNQUFNLENBQUM0QyxDQUFBQSxRQUFTQSxNQUFNUCxNQUFNLEtBQUs7UUFDOUQsTUFBTXlCLGVBQWVsRSxPQUFPSSxNQUFNLENBQUM0QyxDQUFBQSxRQUFTO2dCQUFDO2dCQUFhO2dCQUFhO2FBQVEsQ0FBQ2EsUUFBUSxDQUFDYixNQUFNUCxNQUFNO1FBQ3JHLE1BQU0wQixrQkFBa0JuRSxPQUFPSSxNQUFNLENBQUM0QyxDQUFBQSxRQUFTO2dCQUFDO2dCQUFhO2FBQVksQ0FBQ2EsUUFBUSxDQUFDYixNQUFNUCxNQUFNO1FBRS9GLHFCQUNFLDhEQUFDcUI7WUFBSUgsV0FBVTtzQkFDYiw0RUFBQ0c7Z0JBQUlILFdBQVU7MEJBQ2IsNEVBQUMxRyxrREFBTUEsQ0FBQzZHLEdBQUc7b0JBQ1RNLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUc7b0JBQzdCQyxTQUFTO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUM1QkUsWUFBWTt3QkFBRUMsVUFBVTtvQkFBSTs7c0NBRzVCLDhEQUFDWDs0QkFBSUgsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQUlILFdBQVU7O2tEQUNiLDhEQUFDRzs7MERBQ0MsOERBQUNZO2dEQUFHZixXQUFVOztrRUFDWiw4REFBQ25GLHNNQUFPQTt3REFBQ21GLFdBQVU7Ozs7OztvREFBNEI7Ozs7Ozs7MERBR2pELDhEQUFDZ0I7Z0RBQUVoQixXQUFVOzBEQUFnQjs7Ozs7Ozs7Ozs7O2tEQUcvQiw4REFBQ3JHLHlEQUFNQTt3Q0FDTHlFLFNBQVE7d0NBQ1IyQixTQUFTdEM7d0NBQ1R3QyxVQUFVMUQ7OzBEQUVWLDhEQUFDdEIsc01BQVNBO2dEQUFDK0UsV0FBVyxnQkFBOEMsT0FBOUJ6RCxVQUFVLGlCQUFpQjs7Ozs7OzRDQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTy9FLDhEQUFDNEQ7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDekcscURBQUlBOzhDQUNILDRFQUFDQyw0REFBV0E7d0NBQUN3RyxXQUFVO2tEQUNyQiw0RUFBQ0c7NENBQUlILFdBQVU7OzhEQUNiLDhEQUFDRzs7c0VBQ0MsOERBQUNhOzREQUFFaEIsV0FBVTtzRUFBb0M7Ozs7OztzRUFDakQsOERBQUNnQjs0REFBRWhCLFdBQVU7c0VBQXNDTSxjQUFjVyxNQUFNOzs7Ozs7Ozs7Ozs7OERBRXpFLDhEQUFDN0Ysc01BQVdBO29EQUFDNEUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLN0IsOERBQUN6RyxxREFBSUE7OENBQ0gsNEVBQUNDLDREQUFXQTt3Q0FBQ3dHLFdBQVU7a0RBQ3JCLDRFQUFDRzs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNHOztzRUFDQyw4REFBQ2E7NERBQUVoQixXQUFVO3NFQUFvQzs7Ozs7O3NFQUNqRCw4REFBQ2dCOzREQUFFaEIsV0FBVTtzRUFBb0NPLGFBQWFVLE1BQU07Ozs7Ozs7Ozs7Ozs4REFFdEUsOERBQUN4RyxzTUFBT0E7b0RBQUN1RixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUt6Qiw4REFBQ3pHLHFEQUFJQTs4Q0FDSCw0RUFBQ0MsNERBQVdBO3dDQUFDd0csV0FBVTtrREFDckIsNEVBQUNHOzRDQUFJSCxXQUFVOzs4REFDYiw4REFBQ0c7O3NFQUNDLDhEQUFDYTs0REFBRWhCLFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ2pELDhEQUFDZ0I7NERBQUVoQixXQUFVO3NFQUNWUSxnQkFBZ0IvRCxNQUFNLENBQUM0QyxDQUFBQSxRQUN0QixJQUFJNkIsS0FBSzdCLE1BQU04QixTQUFTLEVBQUVDLFlBQVksT0FBTyxJQUFJRixPQUFPRSxZQUFZLElBQ3BFSCxNQUFNOzs7Ozs7Ozs7Ozs7OERBR1osOERBQUN2RyxzTUFBV0E7b0RBQUNzRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUs3Qiw4REFBQ3pHLHFEQUFJQTs4Q0FDSCw0RUFBQ0MsNERBQVdBO3dDQUFDd0csV0FBVTtrREFDckIsNEVBQUNHOzRDQUFJSCxXQUFVOzs4REFDYiw4REFBQ0c7O3NFQUNDLDhEQUFDYTs0REFBRWhCLFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ2pELDhEQUFDZ0I7NERBQUVoQixXQUFVOztnRUFBcUM7Z0VBQzlDUSxnQkFDQy9ELE1BQU0sQ0FBQzRDLENBQUFBLFFBQ04sSUFBSTZCLEtBQUs3QixNQUFNOEIsU0FBUyxFQUFFQyxZQUFZLE9BQU8sSUFBSUYsT0FBT0UsWUFBWSxNQUNwRS9CLE1BQU1QLE1BQU0sS0FBSyxhQUVsQnVDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLakMsUUFBVWlDLE1BQU9qQyxNQUFNa0MsSUFBSSxDQUFDQyxLQUFLLEdBQUduQyxNQUFNb0MsUUFBUSxFQUFHOzs7Ozs7Ozs7Ozs7OzhEQUl6RSw4REFBQ3BHLHNNQUFVQTtvREFBQzJFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzlCLDhEQUFDL0YscURBQUlBOzRCQUFDeUgsY0FBYTs0QkFBVTFCLFdBQVU7OzhDQUNyQyw4REFBQzdGLHlEQUFRQTtvQ0FBQzZGLFdBQVU7O3NEQUNsQiw4REFBQzVGLDREQUFXQTs0Q0FBQ3VILE9BQU07NENBQVUzQixXQUFVOzs4REFDckMsOERBQUM1RSxzTUFBV0E7b0RBQUM0RSxXQUFVOzs7Ozs7Z0RBQVk7Z0RBQ3pCTSxjQUFjVyxNQUFNO2dEQUFDOzs7Ozs7O3NEQUVqQyw4REFBQzdHLDREQUFXQTs0Q0FBQ3VILE9BQU07NENBQVMzQixXQUFVOzs4REFDcEMsOERBQUN2RixzTUFBT0E7b0RBQUN1RixXQUFVOzs7Ozs7Z0RBQVk7Z0RBQ3RCTyxhQUFhVSxNQUFNO2dEQUFDOzs7Ozs7O3NEQUUvQiw4REFBQzdHLDREQUFXQTs0Q0FBQ3VILE9BQU07NENBQVkzQixXQUFVOzs4REFDdkMsOERBQUN0RixzTUFBV0E7b0RBQUNzRixXQUFVOzs7Ozs7Z0RBQVk7Z0RBQ3ZCUSxnQkFBZ0JTLE1BQU07Z0RBQUM7Ozs7Ozs7Ozs7Ozs7OENBSXZDLDhEQUFDL0csNERBQVdBO29DQUFDeUgsT0FBTTs4Q0FDakIsNEVBQUNDO3dDQUFrQnZGLFFBQVFpRTs7Ozs7Ozs7Ozs7OENBRzdCLDhEQUFDcEcsNERBQVdBO29DQUFDeUgsT0FBTTs4Q0FDakIsNEVBQUNFO3dDQUFpQnhGLFFBQVFrRTs7Ozs7Ozs7Ozs7OENBRzVCLDhEQUFDckcsNERBQVdBO29DQUFDeUgsT0FBTTs4Q0FDakIsNEVBQUNHO3dDQUFvQnpGLFFBQVFtRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTzNDO0lBRUEsNEJBQTRCO0lBQzVCLFNBQVNIO1FBQ1AsTUFBTUUsZUFBZWxFLE9BQU9JLE1BQU0sQ0FBQzRDLENBQUFBLFFBQVMsQ0FBQztnQkFBQztnQkFBYTthQUFZLENBQUNhLFFBQVEsQ0FBQ2IsTUFBTVAsTUFBTTtRQUM3RixNQUFNaUQsYUFBYTFGLE9BQU9JLE1BQU0sQ0FBQzRDLENBQUFBLFFBQVM7Z0JBQUM7Z0JBQWE7YUFBWSxDQUFDYSxRQUFRLENBQUNiLE1BQU1QLE1BQU07UUFFMUYscUJBQ0UsOERBQUNxQjtZQUFJSCxXQUFVO3NCQUNiLDRFQUFDRztnQkFBSUgsV0FBVTswQkFDYiw0RUFBQzFHLGtEQUFNQSxDQUFDNkcsR0FBRztvQkFDVE0sU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRztvQkFBRztvQkFDN0JDLFNBQVM7d0JBQUVGLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUU7b0JBQzVCRSxZQUFZO3dCQUFFQyxVQUFVO29CQUFJOztzQ0FHNUIsOERBQUNYOzRCQUFJSCxXQUFVO3NDQUNiLDRFQUFDRztnQ0FBSUgsV0FBVTs7a0RBQ2IsOERBQUNHOzswREFDQyw4REFBQ1k7Z0RBQUdmLFdBQVU7O2tFQUNaLDhEQUFDOUUsc01BQUlBO3dEQUFDOEUsV0FBVTs7Ozs7O29EQUEwQjs7Ozs7OzswREFHNUMsOERBQUNnQjtnREFBRWhCLFdBQVU7MERBQWdCOzs7Ozs7Ozs7Ozs7a0RBRy9CLDhEQUFDckcseURBQU1BO3dDQUNMeUUsU0FBUTt3Q0FDUjJCLFNBQVN0Qzt3Q0FDVHdDLFVBQVUxRDs7MERBRVYsOERBQUN0QixzTUFBU0E7Z0RBQUMrRSxXQUFXLGdCQUE4QyxPQUE5QnpELFVBQVUsaUJBQWlCOzs7Ozs7NENBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPL0UsOERBQUM0RDs0QkFBSUgsV0FBVTs7OENBQ2IsOERBQUN6RyxxREFBSUE7OENBQ0gsNEVBQUNDLDREQUFXQTt3Q0FBQ3dHLFdBQVU7a0RBQ3JCLDRFQUFDRzs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNHOztzRUFDQyw4REFBQ2E7NERBQUVoQixXQUFVO3NFQUFvQzs7Ozs7O3NFQUNqRCw4REFBQ2dCOzREQUFFaEIsV0FBVTtzRUFBb0NPLGFBQWFVLE1BQU07Ozs7Ozs7Ozs7Ozs4REFFdEUsOERBQUN4RyxzTUFBT0E7b0RBQUN1RixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUt6Qiw4REFBQ3pHLHFEQUFJQTs4Q0FDSCw0RUFBQ0MsNERBQVdBO3dDQUFDd0csV0FBVTtrREFDckIsNEVBQUNHOzRDQUFJSCxXQUFVOzs4REFDYiw4REFBQ0c7O3NFQUNDLDhEQUFDYTs0REFBRWhCLFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ2pELDhEQUFDZ0I7NERBQUVoQixXQUFVO3NFQUFxQzNELE9BQU80RSxNQUFNOzs7Ozs7Ozs7Ozs7OERBRWpFLDhEQUFDNUYsc01BQVVBO29EQUFDMkUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLNUIsOERBQUN6RyxxREFBSUE7OENBQ0gsNEVBQUNDLDREQUFXQTt3Q0FBQ3dHLFdBQVU7a0RBQ3JCLDRFQUFDRzs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNHOztzRUFDQyw4REFBQ2E7NERBQUVoQixXQUFVO3NFQUFvQzs7Ozs7O3NFQUNqRCw4REFBQ2dCOzREQUFFaEIsV0FBVTs7Z0VBQXFDO2dFQUM5QzNELE9BQ0NJLE1BQU0sQ0FBQzRDLENBQUFBLFFBQVNBLE1BQU1QLE1BQU0sS0FBSyxhQUNqQ3VDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLakMsUUFBVWlDLE1BQU9qQyxNQUFNa0MsSUFBSSxDQUFDQyxLQUFLLEdBQUduQyxNQUFNb0MsUUFBUSxFQUFHOzs7Ozs7Ozs7Ozs7OzhEQUl6RSw4REFBQzFHLHNNQUFVQTtvREFBQ2lGLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzlCLDhEQUFDL0YscURBQUlBOzRCQUFDeUgsY0FBYTs0QkFBUzFCLFdBQVU7OzhDQUNwQyw4REFBQzdGLHlEQUFRQTtvQ0FBQzZGLFdBQVU7O3NEQUNsQiw4REFBQzVGLDREQUFXQTs0Q0FBQ3VILE9BQU07NENBQVMzQixXQUFVOzs4REFDcEMsOERBQUN2RixzTUFBT0E7b0RBQUN1RixXQUFVOzs7Ozs7Z0RBQVk7Z0RBQ2ZPLGFBQWFVLE1BQU07Z0RBQUM7Ozs7Ozs7c0RBRXRDLDhEQUFDN0csNERBQVdBOzRDQUFDdUgsT0FBTTs0Q0FBVTNCLFdBQVU7OzhEQUNyQyw4REFBQ3pGLHFNQUFLQTtvREFBQ3lGLFdBQVU7Ozs7OztnREFBWTtnREFDYitCLFdBQVdkLE1BQU07Z0RBQUM7Ozs7Ozs7Ozs7Ozs7OENBSXRDLDhEQUFDL0csNERBQVdBO29DQUFDeUgsT0FBTTs4Q0FDakIsNEVBQUNLO3dDQUFxQjNGLFFBQVFrRTs7Ozs7Ozs7Ozs7OENBR2hDLDhEQUFDckcsNERBQVdBO29DQUFDeUgsT0FBTTs4Q0FDakIsNEVBQUNNO3dDQUFxQjVGLFFBQVEwRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTzVDO0lBRUEsZ0NBQWdDO0lBQ2hDLFNBQVNILGtCQUFrQixLQUErQjtZQUEvQixFQUFFdkYsTUFBTSxFQUF1QixHQUEvQjtRQUN6QixJQUFJQSxPQUFPNEUsTUFBTSxLQUFLLEdBQUc7WUFDdkIscUJBQ0UsOERBQUMxSCxxREFBSUE7MEJBQ0gsNEVBQUNDLDREQUFXQTtvQkFBQ3dHLFdBQVU7O3NDQUNyQiw4REFBQzVFLHNNQUFXQTs0QkFBQzRFLFdBQVU7Ozs7OztzQ0FDdkIsOERBQUNrQzs0QkFBR2xDLFdBQVU7c0NBQXlDOzs7Ozs7c0NBQ3ZELDhEQUFDZ0I7NEJBQUVoQixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJckM7UUFFQSxxQkFDRSw4REFBQ0c7WUFBSUgsV0FBVTtzQkFDWjNELE9BQU84RixHQUFHLENBQUMsQ0FBQzlDLE9BQU8rQyxzQkFDbEIsOERBQUM5SSxrREFBTUEsQ0FBQzZHLEdBQUc7b0JBRVRNLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUcyQixHQUFHLENBQUM7b0JBQUc7b0JBQzlCekIsU0FBUzt3QkFBRUYsU0FBUzt3QkFBRzJCLEdBQUc7b0JBQUU7b0JBQzVCeEIsWUFBWTt3QkFBRXlCLE9BQU9GLFFBQVE7b0JBQUk7OEJBRWpDLDRFQUFDN0kscURBQUlBO3dCQUFDeUcsV0FBVTs7MENBQ2QsOERBQUN2RywyREFBVUE7MENBQ1QsNEVBQUMwRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNHOzs4REFDQyw4REFBQ3pHLDBEQUFTQTtvREFBQ3NHLFdBQVU7OERBQVdYLE1BQU1rQyxJQUFJLENBQUNnQixJQUFJOzs7Ozs7OERBQy9DLDhEQUFDdkI7b0RBQUVoQixXQUFVOzt3REFBd0I7d0RBQVFYLE1BQU1NLEdBQUcsQ0FBQzZDLEtBQUssQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7O3NEQUVoRSw4REFBQzVJLHVEQUFLQTs0Q0FBQ29HLFdBQVU7c0RBQTJCOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLaEQsOERBQUN4Ryw0REFBV0E7O2tEQUNWLDhEQUFDMkc7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDRztnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUM5RSxzTUFBSUE7d0RBQUM4RSxXQUFVOzs7Ozs7a0VBQ2hCLDhEQUFDRzs7MEVBQ0MsOERBQUNhO2dFQUFFaEIsV0FBVTswRUFBd0I7Ozs7OzswRUFDckMsOERBQUNnQjtnRUFBRWhCLFdBQVU7MEVBQWVYLE1BQU1uRCxJQUFJLENBQUNxRyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRy9DLDhEQUFDcEM7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDdkYsc01BQU9BO3dEQUFDdUYsV0FBVTs7Ozs7O2tFQUNuQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFlWCxNQUFNb0MsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUc5Qyw4REFBQ3RCO2dEQUFJSCxXQUFVOztrRUFDYiw4REFBQ2pGLHNNQUFVQTt3REFBQ2lGLFdBQVU7Ozs7OztrRUFDdEIsOERBQUNHOzswRUFDQyw4REFBQ2E7Z0VBQUVoQixXQUFVOzBFQUF3Qjs7Ozs7OzBFQUNyQyw4REFBQ2dCO2dFQUFFaEIsV0FBVTs7b0VBQWM7b0VBQUVYLE1BQU1rQyxJQUFJLENBQUNDLEtBQUssR0FBR25DLE1BQU1vQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtwRSw4REFBQ3RCO3dDQUFJSCxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDeEYsc01BQU1BO3dEQUFDd0YsV0FBVTs7Ozs7O2tFQUNsQiw4REFBQ2dCO3dEQUFFaEIsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFFdkMsOERBQUNnQjtnREFBRWhCLFdBQVU7MERBQWtDWCxNQUFNb0QsZUFBZTs7Ozs7Ozs7Ozs7O29DQUdyRXBELE1BQU1xRCxtQkFBbUIsa0JBQ3hCLDhEQUFDdkM7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDZ0I7Z0RBQUVoQixXQUFVOzBEQUE2Qjs7Ozs7OzBEQUMxQyw4REFBQ2dCO2dEQUFFaEIsV0FBVTswREFBNkRYLE1BQU1xRCxtQkFBbUI7Ozs7Ozs7Ozs7OztrREFJdkcsOERBQUN2Qzt3Q0FBSUgsV0FBVTs7MERBQ2IsOERBQUNyRyx5REFBTUE7Z0RBQ0xvRyxTQUFTLElBQU0xQixrQkFBa0JnQixNQUFNTSxHQUFHLEVBQUU7Z0RBQzVDTSxVQUFVdEQsZUFBZStDLEdBQUcsQ0FBQ0wsTUFBTU0sR0FBRztnREFDdENLLFdBQVU7MERBRVRyRCxlQUFlK0MsR0FBRyxDQUFDTCxNQUFNTSxHQUFHLElBQUksaUJBQWlCOzs7Ozs7MERBRXBELDhEQUFDaEcseURBQU1BO2dEQUNMeUUsU0FBUTtnREFDUjJCLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtnREFDNUNNLFVBQVV0RCxlQUFlK0MsR0FBRyxDQUFDTCxNQUFNTSxHQUFHO2dEQUN0Q0ssV0FBVTswREFFVHJELGVBQWUrQyxHQUFHLENBQUNMLE1BQU1NLEdBQUcsSUFBSSxpQkFBaUI7Ozs7OzswREFFcEQsOERBQUNoRyx5REFBTUE7Z0RBQ0x5RSxTQUFRO2dEQUNSMkIsU0FBUyxJQUFNVCxrQkFBa0JEOztrRUFFakMsOERBQUNyRSxzTUFBR0E7d0RBQUNnRixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21CQTdFbkNYLE1BQU1NLEdBQUc7Ozs7Ozs7Ozs7SUF1RnhCO0lBRUEsK0JBQStCO0lBQy9CLFNBQVNrQyxpQkFBaUIsS0FBK0I7WUFBL0IsRUFBRXhGLE1BQU0sRUFBdUIsR0FBL0I7UUFDeEIsSUFBSUEsT0FBTzRFLE1BQU0sS0FBSyxHQUFHO1lBQ3ZCLHFCQUNFLDhEQUFDMUgscURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUN3RyxXQUFVOztzQ0FDckIsOERBQUN2RixzTUFBT0E7NEJBQUN1RixXQUFVOzs7Ozs7c0NBQ25CLDhEQUFDa0M7NEJBQUdsQyxXQUFVO3NDQUF5Qzs7Ozs7O3NDQUN2RCw4REFBQ2dCOzRCQUFFaEIsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBSXJDO1FBRUEscUJBQ0UsOERBQUNHO1lBQUlILFdBQVU7c0JBQ1ozRCxPQUFPOEYsR0FBRyxDQUFDLENBQUM5QyxPQUFPK0M7Z0JBQ2xCLE1BQU1PLGFBQWFySCxZQUFZLENBQUMrRCxNQUFNUCxNQUFNLENBQThCO2dCQUMxRSxNQUFNOEQsYUFBYUQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZbkgsSUFBSSxLQUFJZixzTUFBT0E7Z0JBRTlDLHFCQUNFLDhEQUFDbkIsa0RBQU1BLENBQUM2RyxHQUFHO29CQUVUTSxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHMkIsR0FBRyxDQUFDO29CQUFHO29CQUM5QnpCLFNBQVM7d0JBQUVGLFNBQVM7d0JBQUcyQixHQUFHO29CQUFFO29CQUM1QnhCLFlBQVk7d0JBQUV5QixPQUFPRixRQUFRO29CQUFJOzhCQUVqQyw0RUFBQzdJLHFEQUFJQTt3QkFBQ3lHLFdBQVcsY0FJaEIsT0FIQ1gsTUFBTVAsTUFBTSxLQUFLLGNBQWMsc0JBQy9CTyxNQUFNUCxNQUFNLEtBQUssY0FBYyx3QkFDL0I7OzBDQUVBLDhEQUFDckYsMkRBQVVBOzBDQUNULDRFQUFDMEc7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDRzs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNHO29EQUFJSCxXQUFXLG9CQUFzQyxPQUFsQjJDLHVCQUFBQSxpQ0FBQUEsV0FBWWxILEtBQUs7OERBQ25ELDRFQUFDbUg7d0RBQVc1QyxXQUFVOzs7Ozs7Ozs7Ozs4REFFeEIsOERBQUNHOztzRUFDQyw4REFBQ3pHLDBEQUFTQTs0REFBQ3NHLFdBQVU7c0VBQVdYLE1BQU1rQyxJQUFJLENBQUNnQixJQUFJOzs7Ozs7c0VBQy9DLDhEQUFDdkI7NERBQUVoQixXQUFVOztnRUFBd0I7Z0VBQVFYLE1BQU1NLEdBQUcsQ0FBQzZDLEtBQUssQ0FBQyxDQUFDO2dFQUFHO2dFQUFJbkQsTUFBTW5ELElBQUksQ0FBQ3FHLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBR3hGLDhEQUFDM0ksdURBQUtBOzRDQUFDb0csV0FBVyxHQUFxQixPQUFsQjJDLHVCQUFBQSxpQ0FBQUEsV0FBWWxILEtBQUssRUFBQztzREFDcENrSCx1QkFBQUEsaUNBQUFBLFdBQVlqSCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdkIsOERBQUNsQyw0REFBV0E7O2tEQUNWLDhEQUFDMkc7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDRztnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUN2RixzTUFBT0E7d0RBQUN1RixXQUFVOzs7Ozs7a0VBQ25CLDhEQUFDRzs7MEVBQ0MsOERBQUNhO2dFQUFFaEIsV0FBVTswRUFBd0I7Ozs7OzswRUFDckMsOERBQUNnQjtnRUFBRWhCLFdBQVU7MEVBQWVYLE1BQU1vQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRzlDLDhEQUFDdEI7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDakYsc01BQVVBO3dEQUFDaUYsV0FBVTs7Ozs7O2tFQUN0Qiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOztvRUFBYztvRUFBRVgsTUFBTWtDLElBQUksQ0FBQ0MsS0FBSyxHQUFHbkMsTUFBTW9DLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR2xFLDhEQUFDdEI7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDbEYsc01BQVFBO3dEQUFDa0YsV0FBVTs7Ozs7O2tFQUNwQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFlLElBQUlrQixLQUFLN0IsTUFBTXdELFlBQVksRUFBRUMsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRy9FLDhEQUFDM0M7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDekYscU1BQUtBO3dEQUFDeUYsV0FBVTs7Ozs7O2tFQUNqQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFlLElBQUlrQixLQUFLN0IsTUFBTThCLFNBQVMsRUFBRTRCLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUs5RSw4REFBQzVDO3dDQUFJSCxXQUFVOzs0Q0FDWlgsTUFBTVAsTUFBTSxLQUFLLDZCQUNoQiw4REFBQ25GLHlEQUFNQTtnREFDTG9HLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtnREFDNUNNLFVBQVV0RCxlQUFlK0MsR0FBRyxDQUFDTCxNQUFNTSxHQUFHO2dEQUN0Q0ssV0FBVTswREFFVHJELGVBQWUrQyxHQUFHLENBQUNMLE1BQU1NLEdBQUcsSUFBSSxnQkFBZ0I7Ozs7Ozs0Q0FHcEROLE1BQU1QLE1BQU0sS0FBSyw2QkFDaEIsOERBQUNuRix5REFBTUE7Z0RBQ0xvRyxTQUFTLElBQU0xQixrQkFBa0JnQixNQUFNTSxHQUFHLEVBQUU7Z0RBQzVDTSxVQUFVdEQsZUFBZStDLEdBQUcsQ0FBQ0wsTUFBTU0sR0FBRztnREFDdENLLFdBQVU7MERBRVRyRCxlQUFlK0MsR0FBRyxDQUFDTCxNQUFNTSxHQUFHLElBQUksZUFBZTs7Ozs7OzRDQUduRE4sTUFBTVAsTUFBTSxLQUFLLHlCQUNoQiw4REFBQ25GLHlEQUFNQTtnREFDTG9HLFNBQVMsSUFBTTFCLGtCQUFrQmdCLE1BQU1NLEdBQUcsRUFBRTtnREFDNUNNLFVBQVV0RCxlQUFlK0MsR0FBRyxDQUFDTCxNQUFNTSxHQUFHO2dEQUN0Q0ssV0FBVTswREFFVHJELGVBQWUrQyxHQUFHLENBQUNMLE1BQU1NLEdBQUcsSUFBSSxrQkFBa0I7Ozs7OzswREFHdkQsOERBQUNoRyx5REFBTUE7Z0RBQ0x5RSxTQUFRO2dEQUNSMkIsU0FBUyxJQUFNVCxrQkFBa0JEOztrRUFFakMsOERBQUNyRSxzTUFBR0E7d0RBQUNnRixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21CQTFGbkNYLE1BQU1NLEdBQUc7Ozs7O1lBa0dwQjs7Ozs7O0lBR047SUFFQSxrQ0FBa0M7SUFDbEMsU0FBU21DLG9CQUFvQixLQUErQjtZQUEvQixFQUFFekYsTUFBTSxFQUF1QixHQUEvQjtRQUMzQixJQUFJQSxPQUFPNEUsTUFBTSxLQUFLLEdBQUc7WUFDdkIscUJBQ0UsOERBQUMxSCxxREFBSUE7MEJBQ0gsNEVBQUNDLDREQUFXQTtvQkFBQ3dHLFdBQVU7O3NDQUNyQiw4REFBQ3RGLHNNQUFXQTs0QkFBQ3NGLFdBQVU7Ozs7OztzQ0FDdkIsOERBQUNrQzs0QkFBR2xDLFdBQVU7c0NBQXlDOzs7Ozs7c0NBQ3ZELDhEQUFDZ0I7NEJBQUVoQixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJckM7UUFFQSxxQkFDRSw4REFBQ0c7WUFBSUgsV0FBVTtzQkFDWjNELE9BQU84RixHQUFHLENBQUMsQ0FBQzlDLE9BQU8rQztnQkFDbEIsTUFBTU8sYUFBYXJILFlBQVksQ0FBQytELE1BQU1QLE1BQU0sQ0FBOEI7Z0JBQzFFLE1BQU04RCxhQUFhRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVluSCxJQUFJLEtBQUlkLHNNQUFXQTtnQkFFbEQscUJBQ0UsOERBQUNwQixrREFBTUEsQ0FBQzZHLEdBQUc7b0JBRVRNLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUcyQixHQUFHLENBQUM7b0JBQUc7b0JBQzlCekIsU0FBUzt3QkFBRUYsU0FBUzt3QkFBRzJCLEdBQUc7b0JBQUU7b0JBQzVCeEIsWUFBWTt3QkFBRXlCLE9BQU9GLFFBQVE7b0JBQUk7OEJBRWpDLDRFQUFDN0kscURBQUlBO3dCQUFDeUcsV0FBVyxjQUVoQixPQURDWCxNQUFNUCxNQUFNLEtBQUssY0FBYyx1QkFBdUI7OzBDQUV0RCw4REFBQ3JGLDJEQUFVQTswQ0FDVCw0RUFBQzBHO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ0c7NENBQUlILFdBQVU7OzhEQUNiLDhEQUFDRztvREFBSUgsV0FBVyxvQkFBc0MsT0FBbEIyQyx1QkFBQUEsaUNBQUFBLFdBQVlsSCxLQUFLOzhEQUNuRCw0RUFBQ21IO3dEQUFXNUMsV0FBVTs7Ozs7Ozs7Ozs7OERBRXhCLDhEQUFDRzs7c0VBQ0MsOERBQUN6RywwREFBU0E7NERBQUNzRyxXQUFVO3NFQUFXWCxNQUFNa0MsSUFBSSxDQUFDZ0IsSUFBSTs7Ozs7O3NFQUMvQyw4REFBQ3ZCOzREQUFFaEIsV0FBVTs7Z0VBQXdCO2dFQUFRWCxNQUFNTSxHQUFHLENBQUM2QyxLQUFLLENBQUMsQ0FBQztnRUFBRztnRUFBSW5ELE1BQU1uRCxJQUFJLENBQUNxRyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUd4Riw4REFBQzNJLHVEQUFLQTs0Q0FBQ29HLFdBQVcsR0FBcUIsT0FBbEIyQyx1QkFBQUEsaUNBQUFBLFdBQVlsSCxLQUFLLEVBQUM7c0RBQ3BDa0gsdUJBQUFBLGlDQUFBQSxXQUFZakgsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSXZCLDhEQUFDbEMsNERBQVdBOztrREFDViw4REFBQzJHO3dDQUFJSCxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDdkYsc01BQU9BO3dEQUFDdUYsV0FBVTs7Ozs7O2tFQUNuQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFlWCxNQUFNb0MsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUc5Qyw4REFBQ3RCO2dEQUFJSCxXQUFVOztrRUFDYiw4REFBQ2pGLHNNQUFVQTt3REFBQ2lGLFdBQVU7Ozs7OztrRUFDdEIsOERBQUNHOzswRUFDQyw4REFBQ2E7Z0VBQUVoQixXQUFVOzBFQUF3Qjs7Ozs7OzBFQUNyQyw4REFBQ2dCO2dFQUFFaEIsV0FBVTswRUFDVlgsTUFBTVAsTUFBTSxLQUFLLGNBQWMsSUFBc0MsT0FBbENPLE1BQU1rQyxJQUFJLENBQUNDLEtBQUssR0FBR25DLE1BQU1vQyxRQUFRLElBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJaEYsOERBQUN0QjtnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUNsRixzTUFBUUE7d0RBQUNrRixXQUFVOzs7Ozs7a0VBQ3BCLDhEQUFDRzs7MEVBQ0MsOERBQUNhO2dFQUFFaEIsV0FBVTswRUFBd0I7Ozs7OzswRUFDckMsOERBQUNnQjtnRUFBRWhCLFdBQVU7MEVBQWUsSUFBSWtCLEtBQUs3QixNQUFNOEIsU0FBUyxFQUFFMkIsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRzVFLDhEQUFDM0M7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDN0Usc01BQUlBO3dEQUFDNkUsV0FBVTs7Ozs7O2tFQUNoQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2pDLDhEQUFDckcseURBQU1BO3dDQUNMeUUsU0FBUTt3Q0FDUjJCLFNBQVMsSUFBTVQsa0JBQWtCRDs7MERBRWpDLDhEQUFDckUsc01BQUdBO2dEQUFDZ0YsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQkE5RGpDWCxNQUFNTSxHQUFHOzs7OztZQXFFcEI7Ozs7OztJQUdOO0lBRUEsbUNBQW1DO0lBQ25DLFNBQVNxQyxxQkFBcUIsS0FBK0I7WUFBL0IsRUFBRTNGLE1BQU0sRUFBdUIsR0FBL0I7UUFDNUIsSUFBSUEsT0FBTzRFLE1BQU0sS0FBSyxHQUFHO1lBQ3ZCLHFCQUNFLDhEQUFDMUgscURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUN3RyxXQUFVOztzQ0FDckIsOERBQUN2RixzTUFBT0E7NEJBQUN1RixXQUFVOzs7Ozs7c0NBQ25CLDhEQUFDa0M7NEJBQUdsQyxXQUFVO3NDQUF5Qzs7Ozs7O3NDQUN2RCw4REFBQ2dCOzRCQUFFaEIsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBSXJDO1FBRUEscUJBQ0UsOERBQUNHO1lBQUlILFdBQVU7c0JBQ1ozRCxPQUFPOEYsR0FBRyxDQUFDLENBQUM5QyxPQUFPK0M7Z0JBQ2xCLE1BQU1PLGFBQWFySCxZQUFZLENBQUMrRCxNQUFNUCxNQUFNLENBQThCO2dCQUMxRSxNQUFNOEQsYUFBYUQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZbkgsSUFBSSxLQUFJakIscU1BQUtBO2dCQUU1QyxxQkFDRSw4REFBQ2pCLGtEQUFNQSxDQUFDNkcsR0FBRztvQkFFVE0sU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRztvQkFBRztvQkFDN0JDLFNBQVM7d0JBQUVGLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUU7b0JBQzVCRSxZQUFZO3dCQUFFeUIsT0FBT0YsUUFBUTtvQkFBSTs4QkFFakMsNEVBQUM3SSxxREFBSUE7d0JBQUN5RyxXQUFVOzswQ0FDZCw4REFBQ3ZHLDJEQUFVQTtnQ0FBQ3VHLFdBQVU7MENBQ3BCLDRFQUFDRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNHOzRDQUFJSCxXQUFVOzs4REFDYiw4REFBQ0c7b0RBQUlILFdBQVcsb0JBQXNDLE9BQWxCMkMsdUJBQUFBLGlDQUFBQSxXQUFZbEgsS0FBSzs4REFDbkQsNEVBQUNtSDt3REFBVzVDLFdBQVU7Ozs7Ozs7Ozs7OzhEQUV4Qiw4REFBQ0c7O3NFQUNDLDhEQUFDekcsMERBQVNBOzREQUFDc0csV0FBVTtzRUFBV1gsTUFBTWtDLElBQUksQ0FBQ2dCLElBQUk7Ozs7OztzRUFDL0MsOERBQUN2Qjs0REFBRWhCLFdBQVU7O2dFQUF3QjtnRUFBUVgsTUFBTU0sR0FBRyxDQUFDNkMsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBR2xFLDhEQUFDNUksdURBQUtBOzRDQUFDb0csV0FBVyxHQUFxQixPQUFsQjJDLHVCQUFBQSxpQ0FBQUEsV0FBWWxILEtBQUssRUFBQztzREFDcENrSCx1QkFBQUEsaUNBQUFBLFdBQVlqSCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLdkIsOERBQUNsQyw0REFBV0E7O2tEQUNWLDhEQUFDMkc7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDRztnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUNuRixzTUFBT0E7d0RBQUNtRixXQUFVOzs7Ozs7a0VBQ25CLDhEQUFDRzs7MEVBQ0MsOERBQUNhO2dFQUFFaEIsV0FBVTswRUFBd0I7Ozs7OzswRUFDckMsOERBQUNnQjtnRUFBRWhCLFdBQVU7MEVBQWVYLE1BQU1PLFFBQVEsQ0FBQzJDLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHbkQsOERBQUNwQztnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUN2RixzTUFBT0E7d0RBQUN1RixXQUFVOzs7Ozs7a0VBQ25CLDhEQUFDRzs7MEVBQ0MsOERBQUNhO2dFQUFFaEIsV0FBVTswRUFBd0I7Ozs7OzswRUFDckMsOERBQUNnQjtnRUFBRWhCLFdBQVU7MEVBQWVYLE1BQU1vQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRzlDLDhEQUFDdEI7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDakYsc01BQVVBO3dEQUFDaUYsV0FBVTs7Ozs7O2tFQUN0Qiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOztvRUFBYztvRUFBRVgsTUFBTWtDLElBQUksQ0FBQ0MsS0FBSyxHQUFHbkMsTUFBTW9DLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTXBFLDhEQUFDdEI7d0NBQUlILFdBQVU7OzBEQUNiLDhEQUFDRztnREFBSUgsV0FBVTs7a0VBQ2IsOERBQUNnRDt3REFBS2hELFdBQVU7a0VBQW9DOzs7Ozs7a0VBQ3BELDhEQUFDZ0Q7d0RBQUtoRCxXQUFVO2tFQUF5QjJDLHVCQUFBQSxpQ0FBQUEsV0FBWWhILFdBQVc7Ozs7Ozs7Ozs7OzswREFHbEUsOERBQUN3RTtnREFBSUgsV0FBVTswREFDWjtvREFBQztvREFBVztvREFBYTtvREFBYTtvREFBUztpREFBWSxDQUFDbUMsR0FBRyxDQUFDLENBQUNyRCxRQUFRbUU7b0RBQ3hFLE1BQU1DLFdBQVc7d0RBQUM7d0RBQVc7d0RBQWE7d0RBQWE7d0RBQVM7cURBQVksQ0FBQ0MsT0FBTyxDQUFDOUQsTUFBTVAsTUFBTSxLQUFLbUU7b0RBQ3RHLE1BQU1HLFlBQVkvRCxNQUFNUCxNQUFNLEtBQUtBO29EQUNuQyxNQUFNdUUsY0FBYzt3REFBQzt3REFBVzt3REFBYTt3REFBYTt3REFBUztxREFBWSxDQUFDRixPQUFPLENBQUM5RCxNQUFNUCxNQUFNLElBQUltRTtvREFFeEcscUJBQ0UsOERBQUM5Qzt3REFBaUJILFdBQVU7OzBFQUMxQiw4REFBQ0c7Z0VBQ0NILFdBQVcsNkRBUVYsT0FQQ3FELGNBQ0ksa0NBQ0FELFlBQ0Usa0RBQ0FGLFdBQ0UsZ0NBQ0E7Ozs7Ozs0REFHWEQsTUFBTSxtQkFDTCw4REFBQzlDO2dFQUNDSCxXQUFXLHlDQUlWLE9BSENxRCxlQUFnQkgsWUFBWUQsTUFBTTtvRUFBQztvRUFBVztvRUFBYTtvRUFBYTtvRUFBUztpRUFBWSxDQUFDRSxPQUFPLENBQUM5RCxNQUFNUCxNQUFNLElBQzlHLGlCQUNBOzs7Ozs7O3VEQWpCRkE7Ozs7O2dEQXVCZDs7Ozs7OzBEQUdGLDhEQUFDcUI7Z0RBQUlILFdBQVU7MERBQ1o7b0RBQUM7b0RBQVc7b0RBQWE7b0RBQWE7b0RBQVM7aURBQVksQ0FBQ21DLEdBQUcsQ0FBQyxDQUFDbUIsT0FBT0wsb0JBQ3ZFLDhEQUFDRDt3REFFQ2hELFdBQVcsdUNBSVYsT0FIQzs0REFBQzs0REFBVzs0REFBYTs0REFBYTs0REFBUzt5REFBWSxDQUFDbUQsT0FBTyxDQUFDOUQsTUFBTVAsTUFBTSxLQUFLbUUsTUFDakYsOEJBQ0E7a0VBR0xLO3VEQVBJQTs7Ozs7Ozs7Ozs7Ozs7OztrREFhYiw4REFBQ25EO3dDQUFJSCxXQUFVOzs0Q0FDWjtnREFBQztnREFBVzs2Q0FBWSxDQUFDRSxRQUFRLENBQUNiLE1BQU1QLE1BQU0sbUJBQzdDLDhEQUFDbkYseURBQU1BO2dEQUNMeUUsU0FBUTtnREFDUjBCLE1BQUs7Z0RBQ0xDLFNBQVMsSUFBTWIsWUFBWUcsTUFBTU0sR0FBRztnREFDcENNLFVBQVV0RCxlQUFlK0MsR0FBRyxDQUFDTCxNQUFNTSxHQUFHOzBEQUVyQ2hELGVBQWUrQyxHQUFHLENBQUNMLE1BQU1NLEdBQUcsSUFBSSxrQkFBa0I7Ozs7OzswREFHdkQsOERBQUNoRyx5REFBTUE7Z0RBQ0x5RSxTQUFRO2dEQUNSMEIsTUFBSztnREFDTEMsU0FBUyxJQUFNVCxrQkFBa0JEOztrRUFFakMsOERBQUNyRSxzTUFBR0E7d0RBQUNnRixXQUFVOzs7Ozs7b0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21CQXhIbkNYLE1BQU1NLEdBQUc7Ozs7O1lBZ0lwQjs7Ozs7O0lBR047SUFFQSxtQ0FBbUM7SUFDbkMsU0FBU3NDLHFCQUFxQixLQUErQjtZQUEvQixFQUFFNUYsTUFBTSxFQUF1QixHQUEvQjtRQUM1QixJQUFJQSxPQUFPNEUsTUFBTSxLQUFLLEdBQUc7WUFDdkIscUJBQ0UsOERBQUMxSCxxREFBSUE7MEJBQ0gsNEVBQUNDLDREQUFXQTtvQkFBQ3dHLFdBQVU7O3NDQUNyQiw4REFBQ3pGLHFNQUFLQTs0QkFBQ3lGLFdBQVU7Ozs7OztzQ0FDakIsOERBQUNrQzs0QkFBR2xDLFdBQVU7c0NBQXlDOzs7Ozs7c0NBQ3ZELDhEQUFDZ0I7NEJBQUVoQixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJckM7UUFFQSxxQkFDRSw4REFBQ0c7WUFBSUgsV0FBVTtzQkFDWjNELE9BQU84RixHQUFHLENBQUMsQ0FBQzlDLE9BQU8rQztnQkFDbEIsTUFBTU8sYUFBYXJILFlBQVksQ0FBQytELE1BQU1QLE1BQU0sQ0FBOEI7Z0JBQzFFLE1BQU04RCxhQUFhRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVluSCxJQUFJLEtBQUlqQixxTUFBS0E7Z0JBRTVDLHFCQUNFLDhEQUFDakIsa0RBQU1BLENBQUM2RyxHQUFHO29CQUVUTSxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHQyxHQUFHO29CQUFHO29CQUM3QkMsU0FBUzt3QkFBRUYsU0FBUzt3QkFBR0MsR0FBRztvQkFBRTtvQkFDNUJFLFlBQVk7d0JBQUV5QixPQUFPRixRQUFRO29CQUFJOzhCQUVqQyw0RUFBQzdJLHFEQUFJQTt3QkFBQ3lHLFdBQVU7OzBDQUNkLDhEQUFDdkcsMkRBQVVBO2dDQUFDdUcsV0FBVTswQ0FDcEIsNEVBQUNHO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ0c7NENBQUlILFdBQVU7OzhEQUNiLDhEQUFDRztvREFBSUgsV0FBVyxvQkFBc0MsT0FBbEIyQyx1QkFBQUEsaUNBQUFBLFdBQVlsSCxLQUFLOzhEQUNuRCw0RUFBQ21IO3dEQUFXNUMsV0FBVTs7Ozs7Ozs7Ozs7OERBRXhCLDhEQUFDRzs7c0VBQ0MsOERBQUN6RywwREFBU0E7NERBQUNzRyxXQUFVO3NFQUFXWCxNQUFNa0MsSUFBSSxDQUFDZ0IsSUFBSTs7Ozs7O3NFQUMvQyw4REFBQ3ZCOzREQUFFaEIsV0FBVTs7Z0VBQXdCO2dFQUMzQlgsTUFBTU0sR0FBRyxDQUFDNkMsS0FBSyxDQUFDLENBQUM7Z0VBQUc7Z0VBQUksSUFBSXRCLEtBQUs3QixNQUFNOEIsU0FBUyxFQUFFMkIsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUlsRiw4REFBQ2xKLHVEQUFLQTs0Q0FBQ29HLFdBQVcsR0FBcUIsT0FBbEIyQyx1QkFBQUEsaUNBQUFBLFdBQVlsSCxLQUFLLEVBQUM7c0RBQ3BDa0gsdUJBQUFBLGlDQUFBQSxXQUFZakgsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS3ZCLDhEQUFDbEMsNERBQVdBOztrREFDViw4REFBQzJHO3dDQUFJSCxXQUFVOzswREFDYiw4REFBQ0c7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDbkYsc01BQU9BO3dEQUFDbUYsV0FBVTs7Ozs7O2tFQUNuQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFlWCxNQUFNTyxRQUFRLENBQUMyQyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR25ELDhEQUFDcEM7Z0RBQUlILFdBQVU7O2tFQUNiLDhEQUFDdkYsc01BQU9BO3dEQUFDdUYsV0FBVTs7Ozs7O2tFQUNuQiw4REFBQ0c7OzBFQUNDLDhEQUFDYTtnRUFBRWhCLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDZ0I7Z0VBQUVoQixXQUFVOzBFQUFlWCxNQUFNb0MsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUc5Qyw4REFBQ3RCO2dEQUFJSCxXQUFVOztrRUFDYiw4REFBQ2pGLHNNQUFVQTt3REFBQ2lGLFdBQVU7Ozs7OztrRUFDdEIsOERBQUNHOzswRUFDQyw4REFBQ2E7Z0VBQUVoQixXQUFVOzBFQUF3Qjs7Ozs7OzBFQUNyQyw4REFBQ2dCO2dFQUFFaEIsV0FBVTs7b0VBQWM7b0VBQUVYLE1BQU1rQyxJQUFJLENBQUNDLEtBQUssR0FBR25DLE1BQU1vQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUdsRSw4REFBQ3RCO2dEQUFJSCxXQUFVOztrRUFDYiw4REFBQ2xGLHNNQUFRQTt3REFBQ2tGLFdBQVU7Ozs7OztrRUFDcEIsOERBQUNHOzswRUFDQyw4REFBQ2E7Z0VBQUVoQixXQUFVOzBFQUF3Qjs7Ozs7OzBFQUNyQyw4REFBQ2dCO2dFQUFFaEIsV0FBVTswRUFBZSxJQUFJa0IsS0FBSzdCLE1BQU13RCxZQUFZLEVBQUVDLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtqRiw4REFBQzNDO3dDQUFJSCxXQUFVOzs0Q0FDWlgsTUFBTVAsTUFBTSxLQUFLLDZCQUNoQiw4REFBQ25GLHlEQUFNQTtnREFDTHlFLFNBQVE7Z0RBQ1IwQixNQUFLO2dEQUNMRSxXQUFVOztrRUFFViw4REFBQzdFLHNNQUFJQTt3REFBQzZFLFdBQVU7Ozs7OztvREFBaUI7Ozs7Ozs7MERBSXJDLDhEQUFDckcseURBQU1BO2dEQUNMeUUsU0FBUTtnREFDUjBCLE1BQUs7Z0RBQ0xDLFNBQVMsSUFBTVQsa0JBQWtCRDs7a0VBRWpDLDhEQUFDckUsc01BQUdBO3dEQUFDZ0YsV0FBVTs7Ozs7O29EQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQkF6RW5DWCxNQUFNTSxHQUFHOzs7OztZQWlGcEI7Ozs7OztJQUdOO0lBRUEsb0RBQW9EO0lBQ3BELHFCQUNFOztZQUNHekQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNa0IsSUFBSSxNQUFLLDJCQUFhLDhEQUFDZ0Q7Ozs7cUNBQXlCLDhEQUFDQzs7Ozs7MEJBR3hELDhEQUFDeEcseURBQU1BO2dCQUFDMEosTUFBTXZHO2dCQUFrQndHLGNBQWN2RzswQkFDNUMsNEVBQUNuRCxnRUFBYUE7b0JBQUNrRyxXQUFVOztzQ0FDdkIsOERBQUNqRywrREFBWUE7c0NBQ1gsNEVBQUNDLDhEQUFXQTswQ0FBQzs7Ozs7Ozs7Ozs7d0JBR2Q4QywrQkFDQyw4REFBQ3FEOzRCQUFJSCxXQUFVOzs4Q0FFYiw4REFBQ0c7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDRzs7OERBQ0MsOERBQUMrQjtvREFBR2xDLFdBQVU7OERBQXlCbEQsY0FBY3lFLElBQUksQ0FBQ2dCLElBQUk7Ozs7Ozs4REFDOUQsOERBQUN2QjtvREFBRWhCLFdBQVU7O3dEQUF3Qjt3REFBUWxELGNBQWM2QyxHQUFHLENBQUM2QyxLQUFLLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7OztzREFFeEUsOERBQUM1SSx1REFBS0E7NENBQUNvRyxXQUFXLEdBQTJGLE9BQXhGMUUsRUFBQUEscUNBQUFBLFlBQVksQ0FBQ3dCLGNBQWNnQyxNQUFNLENBQThCLGNBQS9EeEQseURBQUFBLG1DQUFpRUcsS0FBSyxLQUFJLGVBQWM7c0RBQzFHSCxFQUFBQSxzQ0FBQUEsWUFBWSxDQUFDd0IsY0FBY2dDLE1BQU0sQ0FBOEIsY0FBL0R4RCwwREFBQUEsb0NBQWlFSSxJQUFJLEtBQUlvQixjQUFjZ0MsTUFBTTs7Ozs7Ozs7Ozs7OzhDQUtsRyw4REFBQ3FCO29DQUFJSCxXQUFVOztzREFDYiw4REFBQ0c7NENBQUlILFdBQVU7OzhEQUNiLDhEQUFDRzs7c0VBQ0MsOERBQUNtRDs0REFBTXRELFdBQVU7c0VBQ2Q5RCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rQixJQUFJLE1BQUssYUFBYSxhQUFhOzs7Ozs7c0VBRTVDLDhEQUFDNEQ7NERBQUVoQixXQUFVO3NFQUNWOUQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNa0IsSUFBSSxNQUFLLGFBQWFOLGNBQWNaLElBQUksQ0FBQ3FHLElBQUksR0FBR3pGLGNBQWM4QyxRQUFRLENBQUMyQyxJQUFJOzs7Ozs7Ozs7Ozs7OERBSXRGLDhEQUFDcEM7O3NFQUNDLDhEQUFDbUQ7NERBQU10RCxXQUFVO3NFQUFvQzs7Ozs7O3NFQUNyRCw4REFBQ2dCOzREQUFFaEIsV0FBVTtzRUFBV2xELGNBQWMyRSxRQUFROzs7Ozs7Ozs7Ozs7OERBR2hELDhEQUFDdEI7O3NFQUNDLDhEQUFDbUQ7NERBQU10RCxXQUFVO3NFQUFvQzs7Ozs7O3NFQUNyRCw4REFBQ2dCOzREQUFFaEIsV0FBVTs7Z0VBQXdCO2dFQUFFbEQsY0FBY3lFLElBQUksQ0FBQ0MsS0FBSyxHQUFHMUUsY0FBYzJFLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSTVGLDhEQUFDdEI7NENBQUlILFdBQVU7OzhEQUNiLDhEQUFDRzs7c0VBQ0MsOERBQUNtRDs0REFBTXRELFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ3JELDhEQUFDZ0I7NERBQUVoQixXQUFVO3NFQUFXLElBQUlrQixLQUFLcEUsY0FBYytGLFlBQVksRUFBRUMsa0JBQWtCOzs7Ozs7Ozs7Ozs7OERBR2pGLDhEQUFDM0M7O3NFQUNDLDhEQUFDbUQ7NERBQU10RCxXQUFVO3NFQUFvQzs7Ozs7O3NFQUNyRCw4REFBQ2dCOzREQUFFaEIsV0FBVTtzRUFBVyxJQUFJa0IsS0FBS3BFLGNBQWNxRSxTQUFTLEVBQUUyQixrQkFBa0I7Ozs7Ozs7Ozs7Ozs4REFHOUUsOERBQUMzQzs7c0VBQ0MsOERBQUNtRDs0REFBTXRELFdBQVU7c0VBQW9DOzs7Ozs7c0VBQ3JELDhEQUFDZ0I7NERBQUVoQixXQUFVO3VFQUFXMUUsc0NBQUFBLFlBQVksQ0FBQ3dCLGNBQWNnQyxNQUFNLENBQThCLGNBQS9EeEQsMERBQUFBLG9DQUFpRUssV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU0xRyw4REFBQ3dFOztzREFDQyw4REFBQ21EOzRDQUFNdEQsV0FBVTtzREFBb0M7Ozs7OztzREFDckQsOERBQUNnQjs0Q0FBRWhCLFdBQVU7c0RBQTBDbEQsY0FBYzJGLGVBQWU7Ozs7Ozs7Ozs7OztnQ0FJckYzRixjQUFjNEYsbUJBQW1CLGtCQUNoQyw4REFBQ3ZDOztzREFDQyw4REFBQ21EOzRDQUFNdEQsV0FBVTtzREFBb0M7Ozs7OztzREFDckQsOERBQUNnQjs0Q0FBRWhCLFdBQVU7c0RBQTBDbEQsY0FBYzRGLG1CQUFtQjs7Ozs7Ozs7Ozs7OzhDQUs1Riw4REFBQ3ZDO29DQUFJSCxXQUFVOzt3Q0FDWlQsaUJBQWlCekM7c0RBQ2xCLDhEQUFDbkQseURBQU1BOzRDQUNMeUUsU0FBUTs0Q0FDUjJCLFNBQVMsSUFBTTlDLG9CQUFvQjs0Q0FDbkMrQyxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0dBL25Dd0IvRDs7UUFDRTVCLDBEQUFPQTtRQUNiQyxzREFBUUE7OztLQUZKMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL29yZGVycy9wYWdlLnRzeD82NmZjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIlxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dIZWFkZXIsIERpYWxvZ1RpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIlxuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGFic1wiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvY29udGV4dHMvQXV0aENvbnRleHRcIlxuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tIFwiQC9ob29rcy91c2UtdG9hc3RcIlxuaW1wb3J0IHtcbiAgQ2xvY2ssXG4gIE1hcFBpbixcbiAgUGFja2FnZSxcbiAgQ2hlY2tDaXJjbGUsXG4gIFhDaXJjbGUsXG4gIFRydWNrLFxuICBDaGVmSGF0LFxuICBDYWxlbmRhcixcbiAgRG9sbGFyU2lnbixcbiAgRXllLFxuICBSZWZyZXNoQ3csXG4gIFVzZXIsXG4gIFBob25lLFxuICBNYWlsLFxuICBTdGFyLFxuICBBbGVydENpcmNsZSxcbiAgVHJlbmRpbmdVcCxcbiAgRmlsdGVyLFxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcblxuaW50ZXJmYWNlIE9yZGVyIHtcbiAgX2lkOiBzdHJpbmdcbiAgbWVhbDoge1xuICAgIF9pZDogc3RyaW5nXG4gICAgbmFtZTogc3RyaW5nXG4gICAgcHJpY2U6IG51bWJlclxuICAgIGNhdGVnb3J5OiBzdHJpbmdcbiAgfVxuICB1c2VyOiB7XG4gICAgX2lkOiBzdHJpbmdcbiAgICBuYW1lOiBzdHJpbmdcbiAgfVxuICBwcm92aWRlcjoge1xuICAgIF9pZDogc3RyaW5nXG4gICAgbmFtZTogc3RyaW5nXG4gIH1cbiAgc3RhdHVzOiBzdHJpbmdcbiAgcXVhbnRpdHk6IG51bWJlclxuICBkZWxpdmVyeUFkZHJlc3M6IHN0cmluZ1xuICBkZWxpdmVyeURhdGU6IHN0cmluZ1xuICBzcGVjaWFsSW5zdHJ1Y3Rpb25zPzogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG59XG5cbmNvbnN0IHN0YXR1c0NvbmZpZyA9IHtcbiAgcGVuZGluZzogeyBcbiAgICBpY29uOiBDbG9jaywgXG4gICAgY29sb3I6IFwiYmcteWVsbG93LTUwMFwiLCBcbiAgICB0ZXh0OiBcIlBlbmRpbmdcIixcbiAgICBkZXNjcmlwdGlvbjogXCJXYWl0aW5nIGZvciBjb25maXJtYXRpb25cIlxuICB9LFxuICBjb25maXJtZWQ6IHsgXG4gICAgaWNvbjogQ2hlY2tDaXJjbGUsIFxuICAgIGNvbG9yOiBcImJnLWJsdWUtNTAwXCIsIFxuICAgIHRleHQ6IFwiQ29uZmlybWVkXCIsXG4gICAgZGVzY3JpcHRpb246IFwiT3JkZXIgY29uZmlybWVkIGJ5IGNoZWZcIlxuICB9LFxuICBwcmVwYXJpbmc6IHsgXG4gICAgaWNvbjogQ2hlZkhhdCwgXG4gICAgY29sb3I6IFwiYmctcHVycGxlLTUwMFwiLCBcbiAgICB0ZXh0OiBcIlByZXBhcmluZ1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIllvdXIgbWVhbCBpcyBiZWluZyBwcmVwYXJlZFwiXG4gIH0sXG4gIHJlYWR5OiB7IFxuICAgIGljb246IFBhY2thZ2UsIFxuICAgIGNvbG9yOiBcImJnLW9yYW5nZS01MDBcIiwgXG4gICAgdGV4dDogXCJSZWFkeVwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIlJlYWR5IGZvciBwaWNrdXAvZGVsaXZlcnlcIlxuICB9LFxuICBkZWxpdmVyZWQ6IHsgXG4gICAgaWNvbjogVHJ1Y2ssIFxuICAgIGNvbG9yOiBcImJnLWdyZWVuLTUwMFwiLCBcbiAgICB0ZXh0OiBcIkRlbGl2ZXJlZFwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIk9yZGVyIGNvbXBsZXRlZFwiXG4gIH0sXG4gIGNhbmNlbGxlZDogeyBcbiAgICBpY29uOiBYQ2lyY2xlLCBcbiAgICBjb2xvcjogXCJiZy1yZWQtNTAwXCIsIFxuICAgIHRleHQ6IFwiQ2FuY2VsbGVkXCIsXG4gICAgZGVzY3JpcHRpb246IFwiT3JkZXIgY2FuY2VsbGVkXCJcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBPcmRlcnNQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIHRva2VuIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxuICBjb25zdCBbb3JkZXJzLCBzZXRPcmRlcnNdID0gdXNlU3RhdGU8T3JkZXJbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtmaWx0ZXIsIHNldEZpbHRlcl0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiYWxsXCIpXG4gIGNvbnN0IFt1cGRhdGluZ09yZGVycywgc2V0VXBkYXRpbmdPcmRlcnNdID0gdXNlU3RhdGU8U2V0PHN0cmluZz4+KG5ldyBTZXQoKSlcbiAgY29uc3QgW3NlbGVjdGVkT3JkZXIsIHNldFNlbGVjdGVkT3JkZXJdID0gdXNlU3RhdGU8T3JkZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2hvd09yZGVyRGV0YWlscywgc2V0U2hvd09yZGVyRGV0YWlsc10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPHN0cmluZz4odXNlcj8ucm9sZSA9PT0gXCJwcm92aWRlclwiID8gXCJwZW5kaW5nXCIgOiBcImFjdGl2ZVwiKVxuXG4gIGNvbnN0IEFQSV9CQVNFX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgXCJodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpXCJcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyICYmIHRva2VuKSB7XG4gICAgICBmZXRjaE9yZGVycygpXG4gICAgfVxuICB9LCBbdXNlciwgdG9rZW5dKVxuXG4gIC8vIFJlbW92ZSBhdXRvLXJlZnJlc2ggLSBub3QgbmVlZGVkIGZvciB0aGlzIGltcGxlbWVudGF0aW9uXG5cbiAgY29uc3QgZmV0Y2hPcmRlcnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9vcmRlcnNgLCB7XG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgfSxcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIHNldE9yZGVycyhkYXRhLmRhdGEpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggb3JkZXJzXCIpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIGZldGNoIG9yZGVycy4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCB1cGRhdGVPcmRlclN0YXR1cyA9IGFzeW5jIChvcmRlcklkOiBzdHJpbmcsIG5ld1N0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc2V0VXBkYXRpbmdPcmRlcnMocHJldiA9PiBuZXcgU2V0KHByZXYpLmFkZChvcmRlcklkKSlcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vb3JkZXJzLyR7b3JkZXJJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogXCJQVVRcIixcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1czogbmV3U3RhdHVzIH0pLFxuICAgICAgfSlcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCJPcmRlciB1cGRhdGVkXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGBPcmRlciBzdGF0dXMgY2hhbmdlZCB0byAke3N0YXR1c0NvbmZpZ1tuZXdTdGF0dXMgYXMga2V5b2YgdHlwZW9mIHN0YXR1c0NvbmZpZ10/LnRleHQgfHwgbmV3U3RhdHVzfS5gLFxuICAgICAgICB9KVxuICAgICAgICAvLyBSZWZyZXNoIG9yZGVycyB0byBnZXQgdXBkYXRlZCBkYXRhXG4gICAgICAgIGZldGNoT3JkZXJzKClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5lcnJvciB8fCBcIkZhaWxlZCB0byB1cGRhdGUgb3JkZXJcIilcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJVcGRhdGUgZmFpbGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiRmFpbGVkIHRvIHVwZGF0ZSBvcmRlciBzdGF0dXMuXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFVwZGF0aW5nT3JkZXJzKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpXG4gICAgICAgIG5ld1NldC5kZWxldGUob3JkZXJJZClcbiAgICAgICAgcmV0dXJuIG5ld1NldFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBjYW5jZWxPcmRlciA9IGFzeW5jIChvcmRlcklkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oXCJBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gY2FuY2VsIHRoaXMgb3JkZXI/XCIpKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L29yZGVycy8ke29yZGVySWR9YCwge1xuICAgICAgICBtZXRob2Q6IFwiREVMRVRFXCIsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgfSxcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6IFwiT3JkZXIgY2FuY2VsbGVkXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiWW91ciBvcmRlciBoYXMgYmVlbiBjYW5jZWxsZWQgc3VjY2Vzc2Z1bGx5LlwiLFxuICAgICAgICB9KVxuICAgICAgICAvLyBSZWZyZXNoIG9yZGVycyB0byBzaG93IHVwZGF0ZWQgc3RhdHVzXG4gICAgICAgIGZldGNoT3JkZXJzKClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5lcnJvciB8fCBcIkZhaWxlZCB0byBjYW5jZWwgb3JkZXJcIilcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJDYW5jZWxsYXRpb24gZmFpbGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiRmFpbGVkIHRvIGNhbmNlbCBvcmRlci5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBmaWx0ZXJlZE9yZGVycyA9IG9yZGVycy5maWx0ZXIob3JkZXIgPT4ge1xuICAgIGlmIChmaWx0ZXIgPT09IFwiYWxsXCIpIHJldHVybiB0cnVlXG4gICAgcmV0dXJuIG9yZGVyLnN0YXR1cyA9PT0gZmlsdGVyXG4gIH0pXG5cbiAgY29uc3QgaGFuZGxlVmlld0RldGFpbHMgPSAob3JkZXI6IE9yZGVyKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRPcmRlcihvcmRlcilcbiAgICBzZXRTaG93T3JkZXJEZXRhaWxzKHRydWUpXG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNBY3Rpb25zID0gKG9yZGVyOiBPcmRlcikgPT4ge1xuICAgIGNvbnN0IGFjdGlvbnMgPSBbXVxuICAgIGNvbnN0IGlzVXBkYXRpbmcgPSB1cGRhdGluZ09yZGVycy5oYXMob3JkZXIuX2lkKVxuXG4gICAgaWYgKHVzZXI/LnJvbGUgPT09IFwicHJvdmlkZXJcIiAmJiBvcmRlci5wcm92aWRlci5faWQgPT09IHVzZXIuX2lkKSB7XG4gICAgICAvLyBQcm92aWRlciBhY3Rpb25zXG4gICAgICBpZiAob3JkZXIuc3RhdHVzID09PSBcInBlbmRpbmdcIikge1xuICAgICAgICBhY3Rpb25zLnB1c2goXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAga2V5PVwiY29uZmlybVwiXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdXBkYXRlT3JkZXJTdGF0dXMob3JkZXIuX2lkLCBcImNvbmZpcm1lZFwiKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc1VwZGF0aW5nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc1VwZGF0aW5nID8gXCJVcGRhdGluZy4uLlwiIDogXCJDb25maXJtXCJ9XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIGlmIChvcmRlci5zdGF0dXMgPT09IFwiY29uZmlybWVkXCIpIHtcbiAgICAgICAgYWN0aW9ucy5wdXNoKFxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIGtleT1cInByZXBhcmluZ1wiXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdXBkYXRlT3JkZXJTdGF0dXMob3JkZXIuX2lkLCBcInByZXBhcmluZ1wiKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS01MDAgaG92ZXI6YmctcHVycGxlLTYwMFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNVcGRhdGluZ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNVcGRhdGluZyA/IFwiVXBkYXRpbmcuLi5cIiA6IFwiU3RhcnQgUHJlcGFyaW5nXCJ9XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIGlmIChvcmRlci5zdGF0dXMgPT09IFwicHJlcGFyaW5nXCIpIHtcbiAgICAgICAgYWN0aW9ucy5wdXNoKFxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIGtleT1cInJlYWR5XCJcbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB1cGRhdGVPcmRlclN0YXR1cyhvcmRlci5faWQsIFwicmVhZHlcIil9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAwIGhvdmVyOmJnLW9yYW5nZS02MDBcIlxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVXBkYXRpbmd9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzVXBkYXRpbmcgPyBcIlVwZGF0aW5nLi4uXCIgOiBcIk1hcmsgUmVhZHlcIn1cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgKVxuICAgICAgfVxuICAgICAgaWYgKG9yZGVyLnN0YXR1cyA9PT0gXCJyZWFkeVwiKSB7XG4gICAgICAgIGFjdGlvbnMucHVzaChcbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBrZXk9XCJkZWxpdmVyZWRcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVyLl9pZCwgXCJkZWxpdmVyZWRcIil9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi01MDAgaG92ZXI6YmctZ3JlZW4tNjAwXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc1VwZGF0aW5nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc1VwZGF0aW5nID8gXCJVcGRhdGluZy4uLlwiIDogXCJNYXJrIERlbGl2ZXJlZFwifVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICApXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ3VzdG9tZXIgYWN0aW9uc1xuICAgIGlmICh1c2VyPy5yb2xlID09PSBcImN1c3RvbWVyXCIgJiYgb3JkZXIudXNlci5faWQgPT09IHVzZXIuX2lkKSB7XG4gICAgICBpZiAoW1wicGVuZGluZ1wiLCBcImNvbmZpcm1lZFwiXS5pbmNsdWRlcyhvcmRlci5zdGF0dXMpKSB7XG4gICAgICAgIGFjdGlvbnMucHVzaChcbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBrZXk9XCJjYW5jZWxcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjYW5jZWxPcmRlcihvcmRlci5faWQpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVXBkYXRpbmd9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzVXBkYXRpbmcgPyBcIkNhbmNlbGxpbmcuLi5cIiA6IFwiQ2FuY2VsIE9yZGVyXCJ9XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIClcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gYWN0aW9uc1xuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zMiB3LTMyIGJvcmRlci1iLTIgYm9yZGVyLW9yYW5nZS01MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIFJlbmRlciBkaWZmZXJlbnQgaW50ZXJmYWNlcyBiYXNlZCBvbiB1c2VyIHJvbGVcbiAgaWYgKHVzZXI/LnJvbGUgPT09IFwicHJvdmlkZXJcIikge1xuICAgIHJldHVybiA8Q2hlZk9yZGVyc0ludGVyZmFjZSAvPlxuICB9IGVsc2Uge1xuICAgIHJldHVybiA8Q3VzdG9tZXJPcmRlcnNJbnRlcmZhY2UgLz5cbiAgfVxuXG4gIC8vIENoZWYgT3JkZXJzIEludGVyZmFjZVxuICBmdW5jdGlvbiBDaGVmT3JkZXJzSW50ZXJmYWNlKCkge1xuICAgIGNvbnN0IHBlbmRpbmdPcmRlcnMgPSBvcmRlcnMuZmlsdGVyKG9yZGVyID0+IG9yZGVyLnN0YXR1cyA9PT0gXCJwZW5kaW5nXCIpXG4gICAgY29uc3QgYWN0aXZlT3JkZXJzID0gb3JkZXJzLmZpbHRlcihvcmRlciA9PiBbXCJjb25maXJtZWRcIiwgXCJwcmVwYXJpbmdcIiwgXCJyZWFkeVwiXS5pbmNsdWRlcyhvcmRlci5zdGF0dXMpKVxuICAgIGNvbnN0IGNvbXBsZXRlZE9yZGVycyA9IG9yZGVycy5maWx0ZXIob3JkZXIgPT4gW1wiZGVsaXZlcmVkXCIsIFwiY2FuY2VsbGVkXCJdLmluY2x1ZGVzKG9yZGVyLnN0YXR1cykpXG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiBDaGVmIEhlYWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8Q2hlZkhhdCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtb3JhbmdlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIE9yZGVyIE1hbmFnZW1lbnRcbiAgICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TWFuYWdlIGluY29taW5nIG9yZGVycyBhbmQgdHJhY2sgeW91ciBidXNpbmVzczwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoT3JkZXJzfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2B3LTQgaC00IG1yLTIgJHtsb2FkaW5nID8gXCJhbmltYXRlLXNwaW5cIiA6IFwiXCJ9YH0gLz5cbiAgICAgICAgICAgICAgICAgIFJlZnJlc2hcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENoZWYgU3RhdHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlBlbmRpbmcgT3JkZXJzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXllbGxvdy02MDBcIj57cGVuZGluZ09yZGVycy5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC15ZWxsb3ctNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPkFjdGl2ZSBPcmRlcnM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj57YWN0aXZlT3JkZXJzLmxlbmd0aH08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+Q29tcGxldGVkIFRvZGF5PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvbXBsZXRlZE9yZGVycy5maWx0ZXIob3JkZXIgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3IERhdGUob3JkZXIuY3JlYXRlZEF0KS50b0RhdGVTdHJpbmcoKSA9PT0gbmV3IERhdGUoKS50b0RhdGVTdHJpbmcoKVxuICAgICAgICAgICAgICAgICAgICAgICAgKS5sZW5ndGh9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+VG9kYXkncyBSZXZlbnVlPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOKCuXtjb21wbGV0ZWRPcmRlcnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihvcmRlciA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ldyBEYXRlKG9yZGVyLmNyZWF0ZWRBdCkudG9EYXRlU3RyaW5nKCkgPT09IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCkgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcmRlci5zdGF0dXMgPT09IFwiZGVsaXZlcmVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAucmVkdWNlKChzdW0sIG9yZGVyKSA9PiBzdW0gKyAob3JkZXIubWVhbC5wcmljZSAqIG9yZGVyLnF1YW50aXR5KSwgMClcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtcHVycGxlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENoZWYgT3JkZXIgVGFicyAqL31cbiAgICAgICAgICAgIDxUYWJzIGRlZmF1bHRWYWx1ZT1cInBlbmRpbmdcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0zXCI+XG4gICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwicGVuZGluZ1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBQZW5kaW5nICh7cGVuZGluZ09yZGVycy5sZW5ndGh9KVxuICAgICAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiYWN0aXZlXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgQWN0aXZlICh7YWN0aXZlT3JkZXJzLmxlbmd0aH0pXG4gICAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJjb21wbGV0ZWRcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgQ29tcGxldGVkICh7Y29tcGxldGVkT3JkZXJzLmxlbmd0aH0pXG4gICAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJwZW5kaW5nXCI+XG4gICAgICAgICAgICAgICAgPENoZWZQZW5kaW5nT3JkZXJzIG9yZGVycz17cGVuZGluZ09yZGVyc30gLz5cbiAgICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJhY3RpdmVcIj5cbiAgICAgICAgICAgICAgICA8Q2hlZkFjdGl2ZU9yZGVycyBvcmRlcnM9e2FjdGl2ZU9yZGVyc30gLz5cbiAgICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjb21wbGV0ZWRcIj5cbiAgICAgICAgICAgICAgICA8Q2hlZkNvbXBsZXRlZE9yZGVycyBvcmRlcnM9e2NvbXBsZXRlZE9yZGVyc30gLz5cbiAgICAgICAgICAgICAgPC9UYWJzQ29udGVudD5cbiAgICAgICAgICAgIDwvVGFicz5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8gQ3VzdG9tZXIgT3JkZXJzIEludGVyZmFjZVxuICBmdW5jdGlvbiBDdXN0b21lck9yZGVyc0ludGVyZmFjZSgpIHtcbiAgICBjb25zdCBhY3RpdmVPcmRlcnMgPSBvcmRlcnMuZmlsdGVyKG9yZGVyID0+ICFbXCJkZWxpdmVyZWRcIiwgXCJjYW5jZWxsZWRcIl0uaW5jbHVkZXMob3JkZXIuc3RhdHVzKSlcbiAgICBjb25zdCBwYXN0T3JkZXJzID0gb3JkZXJzLmZpbHRlcihvcmRlciA9PiBbXCJkZWxpdmVyZWRcIiwgXCJjYW5jZWxsZWRcIl0uaW5jbHVkZXMob3JkZXIuc3RhdHVzKSlcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00XCI+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41IH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgey8qIEN1c3RvbWVyIEhlYWRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICBNeSBPcmRlcnNcbiAgICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+VHJhY2sgeW91ciBtZWFsIG9yZGVycyBhbmQgb3JkZXIgaGlzdG9yeTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2ZldGNoT3JkZXJzfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2B3LTQgaC00IG1yLTIgJHtsb2FkaW5nID8gXCJhbmltYXRlLXNwaW5cIiA6IFwiXCJ9YH0gLz5cbiAgICAgICAgICAgICAgICAgIFJlZnJlc2hcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEN1c3RvbWVyIFN0YXRzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02IG1iLThcIj5cbiAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5BY3RpdmUgT3JkZXJzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+e2FjdGl2ZU9yZGVycy5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlRvdGFsIE9yZGVyczwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57b3JkZXJzLmxlbmd0aH08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlRvdGFsIFNwZW50PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOKCuXtvcmRlcnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihvcmRlciA9PiBvcmRlci5zdGF0dXMgPT09IFwiZGVsaXZlcmVkXCIpXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC5yZWR1Y2UoKHN1bSwgb3JkZXIpID0+IHN1bSArIChvcmRlci5tZWFsLnByaWNlICogb3JkZXIucXVhbnRpdHkpLCAwKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1wdXJwbGUtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ3VzdG9tZXIgT3JkZXIgVGFicyAqL31cbiAgICAgICAgICAgIDxUYWJzIGRlZmF1bHRWYWx1ZT1cImFjdGl2ZVwiIGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJhY3RpdmVcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBBY3RpdmUgT3JkZXJzICh7YWN0aXZlT3JkZXJzLmxlbmd0aH0pXG4gICAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJoaXN0b3J5XCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIE9yZGVyIEhpc3RvcnkgKHtwYXN0T3JkZXJzLmxlbmd0aH0pXG4gICAgICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJhY3RpdmVcIj5cbiAgICAgICAgICAgICAgICA8Q3VzdG9tZXJBY3RpdmVPcmRlcnMgb3JkZXJzPXthY3RpdmVPcmRlcnN9IC8+XG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiaGlzdG9yeVwiPlxuICAgICAgICAgICAgICAgIDxDdXN0b21lck9yZGVySGlzdG9yeSBvcmRlcnM9e3Bhc3RPcmRlcnN9IC8+XG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgICAgICA8L1RhYnM+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIENoZWYgUGVuZGluZyBPcmRlcnMgQ29tcG9uZW50XG4gIGZ1bmN0aW9uIENoZWZQZW5kaW5nT3JkZXJzKHsgb3JkZXJzIH06IHsgb3JkZXJzOiBPcmRlcltdIH0pIHtcbiAgICBpZiAob3JkZXJzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTE2XCI+XG4gICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JheS0zMDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIHBlbmRpbmcgb3JkZXJzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5OZXcgb3JkZXJzIHdpbGwgYXBwZWFyIGhlcmUgZm9yIHlvdXIgYXBwcm92YWw8L3A+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICB7b3JkZXJzLm1hcCgob3JkZXIsIGluZGV4KSA9PiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGtleT17b3JkZXIuX2lkfVxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJib3JkZXItbC00IGJvcmRlci1sLXllbGxvdy01MDBcIj5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPntvcmRlci5tZWFsLm5hbWV9PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPk9yZGVyICN7b3JkZXIuX2lkLnNsaWNlKC04KX08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxCYWRnZSBjbGFzc05hbWU9XCJiZy15ZWxsb3ctNTAwIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgTmV3IE9yZGVyXG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkN1c3RvbWVyPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e29yZGVyLnVzZXIubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+UXVhbnRpdHk8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57b3JkZXIucXVhbnRpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8RG9sbGFyU2lnbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkFtb3VudDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPuKCuXtvcmRlci5tZWFsLnByaWNlICogb3JkZXIucXVhbnRpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5EZWxpdmVyeSBBZGRyZXNzPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGJnLWdyYXktNTAgcC0yIHJvdW5kZWRcIj57b3JkZXIuZGVsaXZlcnlBZGRyZXNzfTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHtvcmRlci5zcGVjaWFsSW5zdHJ1Y3Rpb25zICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMVwiPlNwZWNpYWwgSW5zdHJ1Y3Rpb25zOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBiZy15ZWxsb3ctNTAgcC0yIHJvdW5kZWQgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwXCI+e29yZGVyLnNwZWNpYWxJbnN0cnVjdGlvbnN9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB1cGRhdGVPcmRlclN0YXR1cyhvcmRlci5faWQsIFwiY29uZmlybWVkXCIpfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17dXBkYXRpbmdPcmRlcnMuaGFzKG9yZGVyLl9pZCl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7dXBkYXRpbmdPcmRlcnMuaGFzKG9yZGVyLl9pZCkgPyBcIkFjY2VwdGluZy4uLlwiIDogXCJBY2NlcHQgT3JkZXJcIn1cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVyLl9pZCwgXCJjYW5jZWxsZWRcIil9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGluZ09yZGVycy5oYXMob3JkZXIuX2lkKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGJvcmRlci1yZWQtNjAwIGhvdmVyOmJnLXJlZC01MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHt1cGRhdGluZ09yZGVycy5oYXMob3JkZXIuX2lkKSA/IFwiRGVjbGluaW5nLi4uXCIgOiBcIkRlY2xpbmVcIn1cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdEZXRhaWxzKG9yZGVyKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICBWaWV3IERldGFpbHNcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBDaGVmIEFjdGl2ZSBPcmRlcnMgQ29tcG9uZW50XG4gIGZ1bmN0aW9uIENoZWZBY3RpdmVPcmRlcnMoeyBvcmRlcnMgfTogeyBvcmRlcnM6IE9yZGVyW10gfSkge1xuICAgIGlmIChvcmRlcnMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTZcIj5cbiAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctMTYgaC0xNiB0ZXh0LWdyYXktMzAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBhY3RpdmUgb3JkZXJzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5BY2NlcHRlZCBvcmRlcnMgd2lsbCBhcHBlYXIgaGVyZTwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApXG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgIHtvcmRlcnMubWFwKChvcmRlciwgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zdCBzdGF0dXNJbmZvID0gc3RhdHVzQ29uZmlnW29yZGVyLnN0YXR1cyBhcyBrZXlvZiB0eXBlb2Ygc3RhdHVzQ29uZmlnXVxuICAgICAgICAgIGNvbnN0IFN0YXR1c0ljb24gPSBzdGF0dXNJbmZvPy5pY29uIHx8IFBhY2thZ2VcblxuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e29yZGVyLl9pZH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT17YGJvcmRlci1sLTQgJHtcbiAgICAgICAgICAgICAgICBvcmRlci5zdGF0dXMgPT09IFwiY29uZmlybWVkXCIgPyBcImJvcmRlci1sLWJsdWUtNTAwXCIgOlxuICAgICAgICAgICAgICAgIG9yZGVyLnN0YXR1cyA9PT0gXCJwcmVwYXJpbmdcIiA/IFwiYm9yZGVyLWwtcHVycGxlLTUwMFwiIDpcbiAgICAgICAgICAgICAgICBcImJvcmRlci1sLW9yYW5nZS01MDBcIlxuICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1mdWxsICR7c3RhdHVzSW5mbz8uY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdHVzSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj57b3JkZXIubWVhbC5uYW1lfTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+T3JkZXIgI3tvcmRlci5faWQuc2xpY2UoLTgpfSDigKIge29yZGVyLnVzZXIubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPXtgJHtzdGF0dXNJbmZvPy5jb2xvcn0gdGV4dC13aGl0ZWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtzdGF0dXNJbmZvPy50ZXh0fVxuICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5RdWFudGl0eTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e29yZGVyLnF1YW50aXR5fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8RG9sbGFyU2lnbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5BbW91bnQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPuKCuXtvcmRlci5tZWFsLnByaWNlICogb3JkZXIucXVhbnRpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5EZWxpdmVyeSBEYXRlPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57bmV3IERhdGUob3JkZXIuZGVsaXZlcnlEYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPk9yZGVyIFRpbWU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntuZXcgRGF0ZShvcmRlci5jcmVhdGVkQXQpLnRvTG9jYWxlVGltZVN0cmluZygpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtvcmRlci5zdGF0dXMgPT09IFwiY29uZmlybWVkXCIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVyLl9pZCwgXCJwcmVwYXJpbmdcIil9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17dXBkYXRpbmdPcmRlcnMuaGFzKG9yZGVyLl9pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt1cGRhdGluZ09yZGVycy5oYXMob3JkZXIuX2lkKSA/IFwiU3RhcnRpbmcuLi5cIiA6IFwiU3RhcnQgUHJlcGFyaW5nXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIHtvcmRlci5zdGF0dXMgPT09IFwicHJlcGFyaW5nXCIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVyLl9pZCwgXCJyZWFkeVwiKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGluZ09yZGVycy5oYXMob3JkZXIuX2lkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge3VwZGF0aW5nT3JkZXJzLmhhcyhvcmRlci5faWQpID8gXCJNYXJraW5nLi4uXCIgOiBcIk1hcmsgUmVhZHlcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAge29yZGVyLnN0YXR1cyA9PT0gXCJyZWFkeVwiICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB1cGRhdGVPcmRlclN0YXR1cyhvcmRlci5faWQsIFwiZGVsaXZlcmVkXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwZGF0aW5nT3JkZXJzLmhhcyhvcmRlci5faWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge3VwZGF0aW5nT3JkZXJzLmhhcyhvcmRlci5faWQpID8gXCJEZWxpdmVyaW5nLi4uXCIgOiBcIk1hcmsgRGVsaXZlcmVkXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0RldGFpbHMob3JkZXIpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApXG4gICAgICAgIH0pfVxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8gQ2hlZiBDb21wbGV0ZWQgT3JkZXJzIENvbXBvbmVudFxuICBmdW5jdGlvbiBDaGVmQ29tcGxldGVkT3JkZXJzKHsgb3JkZXJzIH06IHsgb3JkZXJzOiBPcmRlcltdIH0pIHtcbiAgICBpZiAob3JkZXJzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTE2XCI+XG4gICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JheS0zMDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIGNvbXBsZXRlZCBvcmRlcnM8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNvbXBsZXRlZCBvcmRlcnMgd2lsbCBhcHBlYXIgaGVyZTwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApXG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgIHtvcmRlcnMubWFwKChvcmRlciwgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zdCBzdGF0dXNJbmZvID0gc3RhdHVzQ29uZmlnW29yZGVyLnN0YXR1cyBhcyBrZXlvZiB0eXBlb2Ygc3RhdHVzQ29uZmlnXVxuICAgICAgICAgIGNvbnN0IFN0YXR1c0ljb24gPSBzdGF0dXNJbmZvPy5pY29uIHx8IENoZWNrQ2lyY2xlXG5cbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PXtvcmRlci5faWR9XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGRlbGF5OiBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9e2Bib3JkZXItbC00ICR7XG4gICAgICAgICAgICAgICAgb3JkZXIuc3RhdHVzID09PSBcImRlbGl2ZXJlZFwiID8gXCJib3JkZXItbC1ncmVlbi01MDBcIiA6IFwiYm9yZGVyLWwtcmVkLTUwMFwiXG4gICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHAtMiByb3VuZGVkLWZ1bGwgJHtzdGF0dXNJbmZvPy5jb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTdGF0dXNJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPntvcmRlci5tZWFsLm5hbWV9PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5PcmRlciAje29yZGVyLl9pZC5zbGljZSgtOCl9IOKAoiB7b3JkZXIudXNlci5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjbGFzc05hbWU9e2Ake3N0YXR1c0luZm8/LmNvbG9yfSB0ZXh0LXdoaXRlYH0+XG4gICAgICAgICAgICAgICAgICAgICAge3N0YXR1c0luZm8/LnRleHR9XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC00IG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlF1YW50aXR5PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57b3JkZXIucXVhbnRpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkVhcm5pbmdzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge29yZGVyLnN0YXR1cyA9PT0gXCJkZWxpdmVyZWRcIiA/IGDigrkke29yZGVyLm1lYWwucHJpY2UgKiBvcmRlci5xdWFudGl0eX1gIDogXCLigrkwXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkNvbXBsZXRlZCBEYXRlPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57bmV3IERhdGUob3JkZXIuY3JlYXRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+UmF0aW5nPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5Ob3QgcmF0ZWQgeWV0PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0RldGFpbHMob3JkZXIpfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIClcbiAgICAgICAgfSl9XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBDdXN0b21lciBBY3RpdmUgT3JkZXJzIENvbXBvbmVudFxuICBmdW5jdGlvbiBDdXN0b21lckFjdGl2ZU9yZGVycyh7IG9yZGVycyB9OiB7IG9yZGVyczogT3JkZXJbXSB9KSB7XG4gICAgaWYgKG9yZGVycy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xNlwiPlxuICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JheS0zMDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIGFjdGl2ZSBvcmRlcnM8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPllvdXIgYWN0aXZlIG9yZGVycyB3aWxsIGFwcGVhciBoZXJlPC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIClcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAge29yZGVycy5tYXAoKG9yZGVyLCBpbmRleCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHN0YXR1c0luZm8gPSBzdGF0dXNDb25maWdbb3JkZXIuc3RhdHVzIGFzIGtleW9mIHR5cGVvZiBzdGF0dXNDb25maWddXG4gICAgICAgICAgY29uc3QgU3RhdHVzSWNvbiA9IHN0YXR1c0luZm8/Lmljb24gfHwgQ2xvY2tcblxuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e29yZGVyLl9pZH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtZnVsbCAke3N0YXR1c0luZm8/LmNvbG9yfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXR1c0ljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+e29yZGVyLm1lYWwubmFtZX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPk9yZGVyICN7b3JkZXIuX2lkLnNsaWNlKC04KX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPXtgJHtzdGF0dXNJbmZvPy5jb2xvcn0gdGV4dC13aGl0ZWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtzdGF0dXNJbmZvPy50ZXh0fVxuICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuXG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00IG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxDaGVmSGF0IGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkNoZWY8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntvcmRlci5wcm92aWRlci5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5RdWFudGl0eTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e29yZGVyLnF1YW50aXR5fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8RG9sbGFyU2lnbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5Ub3RhbDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+4oK5e29yZGVyLm1lYWwucHJpY2UgKiBvcmRlci5xdWFudGl0eX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBPcmRlciBQcm9ncmVzcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+T3JkZXIgUHJvZ3Jlc3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3N0YXR1c0luZm8/LmRlc2NyaXB0aW9ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7W1wicGVuZGluZ1wiLCBcImNvbmZpcm1lZFwiLCBcInByZXBhcmluZ1wiLCBcInJlYWR5XCIsIFwiZGVsaXZlcmVkXCJdLm1hcCgoc3RhdHVzLCBpZHgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gW1wicGVuZGluZ1wiLCBcImNvbmZpcm1lZFwiLCBcInByZXBhcmluZ1wiLCBcInJlYWR5XCIsIFwiZGVsaXZlcmVkXCJdLmluZGV4T2Yob3JkZXIuc3RhdHVzKSA+PSBpZHhcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudCA9IG9yZGVyLnN0YXR1cyA9PT0gc3RhdHVzXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0NvbXBsZXRlZCA9IFtcInBlbmRpbmdcIiwgXCJjb25maXJtZWRcIiwgXCJwcmVwYXJpbmdcIiwgXCJyZWFkeVwiLCBcImRlbGl2ZXJlZFwiXS5pbmRleE9mKG9yZGVyLnN0YXR1cykgPiBpZHhcblxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3N0YXR1c30gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCBib3JkZXItMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDb21wbGV0ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctZ3JlZW4tNTAwIGJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogaXNDdXJyZW50XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctb3JhbmdlLTUwMCBib3JkZXItb3JhbmdlLTUwMCBhbmltYXRlLXB1bHNlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWJsdWUtNTAwIGJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTIwMCBib3JkZXItZ3JheS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aWR4IDwgNCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctOCBoLTAuNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NvbXBsZXRlZCB8fCAoaXNBY3RpdmUgJiYgaWR4IDwgW1wicGVuZGluZ1wiLCBcImNvbmZpcm1lZFwiLCBcInByZXBhcmluZ1wiLCBcInJlYWR5XCIsIFwiZGVsaXZlcmVkXCJdLmluZGV4T2Yob3JkZXIuc3RhdHVzKSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7W1wiUGVuZGluZ1wiLCBcIkNvbmZpcm1lZFwiLCBcIlByZXBhcmluZ1wiLCBcIlJlYWR5XCIsIFwiRGVsaXZlcmVkXCJdLm1hcCgobGFiZWwsIGlkeCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtsYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbXCJwZW5kaW5nXCIsIFwiY29uZmlybWVkXCIsIFwicHJlcGFyaW5nXCIsIFwicmVhZHlcIiwgXCJkZWxpdmVyZWRcIl0uaW5kZXhPZihvcmRlci5zdGF0dXMpID49IGlkeFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtZ3JheS03MDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAge1tcInBlbmRpbmdcIiwgXCJjb25maXJtZWRcIl0uaW5jbHVkZXMob3JkZXIuc3RhdHVzKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImRlc3RydWN0aXZlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjYW5jZWxPcmRlcihvcmRlci5faWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwZGF0aW5nT3JkZXJzLmhhcyhvcmRlci5faWQpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt1cGRhdGluZ09yZGVycy5oYXMob3JkZXIuX2lkKSA/IFwiQ2FuY2VsbGluZy4uLlwiIDogXCJDYW5jZWwgT3JkZXJcIn1cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdEZXRhaWxzKG9yZGVyKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBWaWV3IERldGFpbHNcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIEN1c3RvbWVyIE9yZGVyIEhpc3RvcnkgQ29tcG9uZW50XG4gIGZ1bmN0aW9uIEN1c3RvbWVyT3JkZXJIaXN0b3J5KHsgb3JkZXJzIH06IHsgb3JkZXJzOiBPcmRlcltdIH0pIHtcbiAgICBpZiAob3JkZXJzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTE2XCI+XG4gICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JheS0zMDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPk5vIG9yZGVyIGhpc3Rvcnk8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPllvdXIgY29tcGxldGVkIG9yZGVycyB3aWxsIGFwcGVhciBoZXJlPC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIClcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAge29yZGVycy5tYXAoKG9yZGVyLCBpbmRleCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHN0YXR1c0luZm8gPSBzdGF0dXNDb25maWdbb3JkZXIuc3RhdHVzIGFzIGtleW9mIHR5cGVvZiBzdGF0dXNDb25maWddXG4gICAgICAgICAgY29uc3QgU3RhdHVzSWNvbiA9IHN0YXR1c0luZm8/Lmljb24gfHwgQ2xvY2tcblxuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBrZXk9e29yZGVyLl9pZH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwib3BhY2l0eS05MCBob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHlcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1mdWxsICR7c3RhdHVzSW5mbz8uY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdHVzSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj57b3JkZXIubWVhbC5uYW1lfTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIE9yZGVyICN7b3JkZXIuX2lkLnNsaWNlKC04KX0g4oCiIHtuZXcgRGF0ZShvcmRlci5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT17YCR7c3RhdHVzSW5mbz8uY29sb3J9IHRleHQtd2hpdGVgfT5cbiAgICAgICAgICAgICAgICAgICAgICB7c3RhdHVzSW5mbz8udGV4dH1cbiAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cblxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hlZkhhdCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5DaGVmPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57b3JkZXIucHJvdmlkZXIubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+UXVhbnRpdHk8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntvcmRlci5xdWFudGl0eX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+VG90YWwgUGFpZDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+4oK5e29yZGVyLm1lYWwucHJpY2UgKiBvcmRlci5xdWFudGl0eX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkRlbGl2ZXJ5IERhdGU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntuZXcgRGF0ZShvcmRlci5kZWxpdmVyeURhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtvcmRlci5zdGF0dXMgPT09IFwiZGVsaXZlcmVkXCIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy02MDAgYm9yZGVyLXllbGxvdy02MDAgaG92ZXI6YmcteWVsbG93LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgUmF0ZSBPcmRlclxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0RldGFpbHMob3JkZXIpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApXG4gICAgICAgIH0pfVxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8gTWFpbiByZW5kZXIgLSBjaG9vc2UgaW50ZXJmYWNlIGJhc2VkIG9uIHVzZXIgcm9sZVxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7dXNlcj8ucm9sZSA9PT0gXCJwcm92aWRlclwiID8gPENoZWZPcmRlcnNJbnRlcmZhY2UgLz4gOiA8Q3VzdG9tZXJPcmRlcnNJbnRlcmZhY2UgLz59XG5cbiAgICAgIHsvKiBPcmRlciBEZXRhaWxzIE1vZGFsICovfVxuICAgICAgPERpYWxvZyBvcGVuPXtzaG93T3JkZXJEZXRhaWxzfSBvbk9wZW5DaGFuZ2U9e3NldFNob3dPcmRlckRldGFpbHN9PlxuICAgICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJtYXgtdy0yeGwgbWF4LWgtWzgwdmhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICA8RGlhbG9nVGl0bGU+T3JkZXIgRGV0YWlsczwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgICB7c2VsZWN0ZWRPcmRlciAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICB7LyogT3JkZXIgSGVhZGVyICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGRcIj57c2VsZWN0ZWRPcmRlci5tZWFsLm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPk9yZGVyICN7c2VsZWN0ZWRPcmRlci5faWQuc2xpY2UoLTgpfTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8QmFkZ2UgY2xhc3NOYW1lPXtgJHtzdGF0dXNDb25maWdbc2VsZWN0ZWRPcmRlci5zdGF0dXMgYXMga2V5b2YgdHlwZW9mIHN0YXR1c0NvbmZpZ10/LmNvbG9yIHx8IFwiYmctZ3JheS01MDBcIn0gdGV4dC13aGl0ZWB9PlxuICAgICAgICAgICAgICAgICAge3N0YXR1c0NvbmZpZ1tzZWxlY3RlZE9yZGVyLnN0YXR1cyBhcyBrZXlvZiB0eXBlb2Ygc3RhdHVzQ29uZmlnXT8udGV4dCB8fCBzZWxlY3RlZE9yZGVyLnN0YXR1c31cbiAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogT3JkZXIgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09IFwicHJvdmlkZXJcIiA/IFwiQ3VzdG9tZXJcIiA6IFwiQ2hlZlwifVxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09IFwicHJvdmlkZXJcIiA/IHNlbGVjdGVkT3JkZXIudXNlci5uYW1lIDogc2VsZWN0ZWRPcmRlci5wcm92aWRlci5uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlF1YW50aXR5PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbVwiPntzZWxlY3RlZE9yZGVyLnF1YW50aXR5fTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+VG90YWwgQW1vdW50PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkXCI+4oK5e3NlbGVjdGVkT3JkZXIubWVhbC5wcmljZSAqIHNlbGVjdGVkT3JkZXIucXVhbnRpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPkRlbGl2ZXJ5IERhdGU8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e25ldyBEYXRlKHNlbGVjdGVkT3JkZXIuZGVsaXZlcnlEYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPk9yZGVyIERhdGU8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e25ldyBEYXRlKHNlbGVjdGVkT3JkZXIuY3JlYXRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlN0YXR1czwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj57c3RhdHVzQ29uZmlnW3NlbGVjdGVkT3JkZXIuc3RhdHVzIGFzIGtleW9mIHR5cGVvZiBzdGF0dXNDb25maWddPy5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIERlbGl2ZXJ5IEFkZHJlc3MgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPkRlbGl2ZXJ5IEFkZHJlc3M8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbXQtMSBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+e3NlbGVjdGVkT3JkZXIuZGVsaXZlcnlBZGRyZXNzfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFNwZWNpYWwgSW5zdHJ1Y3Rpb25zICovfVxuICAgICAgICAgICAgICB7c2VsZWN0ZWRPcmRlci5zcGVjaWFsSW5zdHJ1Y3Rpb25zICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlNwZWNpYWwgSW5zdHJ1Y3Rpb25zPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbXQtMSBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+e3NlbGVjdGVkT3JkZXIuc3BlY2lhbEluc3RydWN0aW9uc308L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTIgcHQtNCBib3JkZXItdFwiPlxuICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNBY3Rpb25zKHNlbGVjdGVkT3JkZXIpfVxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dPcmRlckRldGFpbHMoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtYXV0b1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgQ2xvc2VcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICA8L0RpYWxvZz5cbiAgICA8Lz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwibW90aW9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIkJhZGdlIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsInVzZUF1dGgiLCJ1c2VUb2FzdCIsIkNsb2NrIiwiTWFwUGluIiwiUGFja2FnZSIsIkNoZWNrQ2lyY2xlIiwiWENpcmNsZSIsIlRydWNrIiwiQ2hlZkhhdCIsIkNhbGVuZGFyIiwiRG9sbGFyU2lnbiIsIkV5ZSIsIlJlZnJlc2hDdyIsIlVzZXIiLCJTdGFyIiwiQWxlcnRDaXJjbGUiLCJUcmVuZGluZ1VwIiwic3RhdHVzQ29uZmlnIiwicGVuZGluZyIsImljb24iLCJjb2xvciIsInRleHQiLCJkZXNjcmlwdGlvbiIsImNvbmZpcm1lZCIsInByZXBhcmluZyIsInJlYWR5IiwiZGVsaXZlcmVkIiwiY2FuY2VsbGVkIiwiT3JkZXJzUGFnZSIsInVzZXIiLCJ0b2tlbiIsInRvYXN0Iiwib3JkZXJzIiwic2V0T3JkZXJzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJmaWx0ZXIiLCJzZXRGaWx0ZXIiLCJ1cGRhdGluZ09yZGVycyIsInNldFVwZGF0aW5nT3JkZXJzIiwiU2V0Iiwic2VsZWN0ZWRPcmRlciIsInNldFNlbGVjdGVkT3JkZXIiLCJzaG93T3JkZXJEZXRhaWxzIiwic2V0U2hvd09yZGVyRGV0YWlscyIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInJvbGUiLCJBUElfQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImZldGNoT3JkZXJzIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwib2siLCJkYXRhIiwianNvbiIsIkVycm9yIiwiZXJyb3IiLCJ0aXRsZSIsInZhcmlhbnQiLCJ1cGRhdGVPcmRlclN0YXR1cyIsIm9yZGVySWQiLCJuZXdTdGF0dXMiLCJwcmV2IiwiYWRkIiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdGF0dXMiLCJtZXNzYWdlIiwibmV3U2V0IiwiZGVsZXRlIiwiY2FuY2VsT3JkZXIiLCJjb25maXJtIiwiZmlsdGVyZWRPcmRlcnMiLCJvcmRlciIsImhhbmRsZVZpZXdEZXRhaWxzIiwiZ2V0U3RhdHVzQWN0aW9ucyIsImFjdGlvbnMiLCJpc1VwZGF0aW5nIiwiaGFzIiwiX2lkIiwicHJvdmlkZXIiLCJwdXNoIiwic2l6ZSIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJkaXNhYmxlZCIsImluY2x1ZGVzIiwiZGl2IiwiQ2hlZk9yZGVyc0ludGVyZmFjZSIsIkN1c3RvbWVyT3JkZXJzSW50ZXJmYWNlIiwicGVuZGluZ09yZGVycyIsImFjdGl2ZU9yZGVycyIsImNvbXBsZXRlZE9yZGVycyIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJoMSIsInAiLCJsZW5ndGgiLCJEYXRlIiwiY3JlYXRlZEF0IiwidG9EYXRlU3RyaW5nIiwicmVkdWNlIiwic3VtIiwibWVhbCIsInByaWNlIiwicXVhbnRpdHkiLCJkZWZhdWx0VmFsdWUiLCJ2YWx1ZSIsIkNoZWZQZW5kaW5nT3JkZXJzIiwiQ2hlZkFjdGl2ZU9yZGVycyIsIkNoZWZDb21wbGV0ZWRPcmRlcnMiLCJwYXN0T3JkZXJzIiwiQ3VzdG9tZXJBY3RpdmVPcmRlcnMiLCJDdXN0b21lck9yZGVySGlzdG9yeSIsImgzIiwibWFwIiwiaW5kZXgiLCJ4IiwiZGVsYXkiLCJuYW1lIiwic2xpY2UiLCJkZWxpdmVyeUFkZHJlc3MiLCJzcGVjaWFsSW5zdHJ1Y3Rpb25zIiwic3RhdHVzSW5mbyIsIlN0YXR1c0ljb24iLCJkZWxpdmVyeURhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJzcGFuIiwiaWR4IiwiaXNBY3RpdmUiLCJpbmRleE9mIiwiaXNDdXJyZW50IiwiaXNDb21wbGV0ZWQiLCJsYWJlbCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});