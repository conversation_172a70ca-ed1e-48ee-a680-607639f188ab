"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Auto-refresh orders every 20 seconds for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            const interval = setInterval(()=>{\n                fetchOrders();\n            }, 20000) // 20 seconds\n            ;\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        user,\n        token\n    ]);\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        // Add to updating set for loading state\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        // Optimistically update the UI first\n        setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                    ...order,\n                    status: newStatus\n                } : order));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                const data = await response.json();\n                // Update with the actual response data\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            ...data.data\n                        } : order));\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n            } else {\n                // Revert the optimistic update on error\n                fetchOrders();\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            // Revert the optimistic update on error\n            fetchOrders();\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            // Remove from updating set\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        // Optimistically update the UI first\n        setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                    ...order,\n                    status: \"cancelled\"\n                } : order));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                // Remove the order from the list or mark as cancelled\n                const data = await response.json();\n                if (data.data && Object.keys(data.data).length === 0) {\n                    // Order was deleted, remove from list\n                    setOrders((prevOrders)=>prevOrders.filter((order)=>order._id !== orderId));\n                } else {\n                    // Order was marked as cancelled\n                    setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                                ...order,\n                                status: \"cancelled\"\n                            } : order));\n                }\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n            } else {\n                // Revert the optimistic update on error\n                fetchOrders();\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            // Revert the optimistic update on error\n            fetchOrders();\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 347,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Order Management\" : \"My Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Manage orders for your meals\" : \"Track your meal orders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Auto-updating every 20s\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mb-8\",\n                        children: [\n                            \"all\",\n                            \"pending\",\n                            \"confirmed\",\n                            \"preparing\",\n                            \"ready\",\n                            \"delivered\",\n                            \"cancelled\"\n                        ].map((status)=>{\n                            var _statusConfig_status;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: filter === status ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setFilter(status),\n                                className: \"capitalize\",\n                                children: [\n                                    status === \"all\" ? \"All Orders\" : ((_statusConfig_status = statusConfig[status]) === null || _statusConfig_status === void 0 ? void 0 : _statusConfig_status.text) || status,\n                                    status !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: orders.filter((order)=>order.status === status).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No orders found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: filter === \"all\" ? \"You don't have any orders yet.\" : 'No orders with status \"'.concat(filter, '\".')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, this) : filteredOrders.map((order, index)=>{\n                            var _statusConfig_order_status;\n                            const StatusIcon = ((_statusConfig_order_status = statusConfig[order.status]) === null || _statusConfig_order_status === void 0 ? void 0 : _statusConfig_order_status.icon) || _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                            const statusInfo = statusConfig[order.status];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    scale: updatingOrders.has(order._id) ? 0.98 : 1\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    scale: {\n                                        duration: 0.2\n                                    }\n                                },\n                                className: updatingOrders.has(order._id) ? \"ring-2 ring-orange-300 ring-opacity-50\" : \"\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-full \".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-lg\",\n                                                                        children: order.meal.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Order #\",\n                                                                            order._id.slice(-8)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"\".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\", \" text-white\"),\n                                                        children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text) || order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Total Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 466,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₹\",\n                                                                                order.meal.price * order.quantity\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Quantity\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: order.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Delivery Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: new Date(order.deliveryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Address\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold text-sm\",\n                                                                            children: order.deliveryAddress\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? order.user.name : order.provider.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Order Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: new Date(order.createdAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 25\n                                                }, this),\n                                                order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                            children: \"Special Instructions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: order.specialInstructions\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        getStatusActions(order),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"View Details\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 21\n                                }, this)\n                            }, order._id, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 19\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 355,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 354,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"TDI6pD3jZn9LrtimiB8C6q9axmU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});