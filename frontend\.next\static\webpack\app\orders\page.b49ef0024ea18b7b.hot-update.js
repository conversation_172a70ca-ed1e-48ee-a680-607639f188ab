"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Remove auto-refresh - not needed for this implementation\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n                // Refresh orders to get updated data\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n                // Refresh orders to show updated status\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const handleViewDetails = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Order Management\" : \"My Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Manage orders for your meals\" : \"Track your meal orders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: fetchOrders,\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mb-8\",\n                        children: [\n                            \"all\",\n                            \"pending\",\n                            \"confirmed\",\n                            \"preparing\",\n                            \"ready\",\n                            \"delivered\",\n                            \"cancelled\"\n                        ].map((status)=>{\n                            var _statusConfig_status;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: filter === status ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setFilter(status),\n                                className: \"capitalize\",\n                                children: [\n                                    status === \"all\" ? \"All Orders\" : ((_statusConfig_status = statusConfig[status]) === null || _statusConfig_status === void 0 ? void 0 : _statusConfig_status.text) || status,\n                                    status !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: orders.filter((order)=>order.status === status).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No orders found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: filter === \"all\" ? \"You don't have any orders yet.\" : 'No orders with status \"'.concat(filter, '\".')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 15\n                        }, this) : filteredOrders.map((order, index)=>{\n                            var _statusConfig_order_status;\n                            const StatusIcon = ((_statusConfig_order_status = statusConfig[order.status]) === null || _statusConfig_order_status === void 0 ? void 0 : _statusConfig_order_status.icon) || _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                            const statusInfo = statusConfig[order.status];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    scale: updatingOrders.has(order._id) ? 0.98 : 1\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    scale: {\n                                        duration: 0.2\n                                    }\n                                },\n                                className: updatingOrders.has(order._id) ? \"ring-2 ring-orange-300 ring-opacity-50\" : \"\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-full \".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-lg\",\n                                                                        children: order.meal.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Order #\",\n                                                                            order._id.slice(-8)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"\".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\", \" text-white\"),\n                                                        children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text) || order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Total Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₹\",\n                                                                                order.meal.price * order.quantity\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Quantity\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: order.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Delivery Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: new Date(order.deliveryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Address\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold text-sm\",\n                                                                            children: order.deliveryAddress\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? order.user.name : order.provider.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Order Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: new Date(order.createdAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 25\n                                                }, this),\n                                                order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                            children: \"Special Instructions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: order.specialInstructions\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Order Progress\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                \"pending\",\n                                                                \"confirmed\",\n                                                                \"preparing\",\n                                                                \"ready\",\n                                                                \"delivered\"\n                                                            ].map((status, idx)=>{\n                                                                const isActive = [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) >= idx;\n                                                                const isCurrent = order.status === status;\n                                                                const isCompleted = [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) > idx;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full border-2 transition-all duration-300 \".concat(isCompleted ? \"bg-green-500 border-green-500\" : isCurrent ? \"bg-orange-500 border-orange-500 animate-pulse\" : isActive ? \"bg-blue-500 border-blue-500\" : \"bg-gray-200 border-gray-300\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        idx < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-0.5 transition-all duration-300 \".concat(isCompleted || isActive && idx < [\n                                                                                \"pending\",\n                                                                                \"confirmed\",\n                                                                                \"preparing\",\n                                                                                \"ready\",\n                                                                                \"delivered\"\n                                                                            ].indexOf(order.status) ? \"bg-green-500\" : \"bg-gray-200\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, status, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between mt-1\",\n                                                            children: [\n                                                                \"Pending\",\n                                                                \"Confirmed\",\n                                                                \"Preparing\",\n                                                                \"Ready\",\n                                                                \"Delivered\"\n                                                            ].map((label, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs transition-all duration-300 \".concat([\n                                                                        \"pending\",\n                                                                        \"confirmed\",\n                                                                        \"preparing\",\n                                                                        \"ready\",\n                                                                        \"delivered\"\n                                                                    ].indexOf(order.status) >= idx ? \"text-gray-700 font-medium\" : \"text-gray-400\"),\n                                                                    children: label\n                                                                }, label, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        getStatusActions(order),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"View Details\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 21\n                                }, this)\n                            }, order._id, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 19\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 304,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 303,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"/rsJJBa7zh4RQKMamCNqHzRuT48=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});