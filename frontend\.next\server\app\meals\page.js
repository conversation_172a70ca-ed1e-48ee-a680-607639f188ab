/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/meals/page";
exports.ids = ["app/meals/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmeals%2Fpage&page=%2Fmeals%2Fpage&appPaths=%2Fmeals%2Fpage&pagePath=private-next-app-dir%2Fmeals%2Fpage.tsx&appDir=D%3A%5Cgithub%5CMealMate%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5CMealMate%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmeals%2Fpage&page=%2Fmeals%2Fpage&appPaths=%2Fmeals%2Fpage&pagePath=private-next-app-dir%2Fmeals%2Fpage.tsx&appDir=D%3A%5Cgithub%5CMealMate%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5CMealMate%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'meals',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/meals/page.tsx */ \"(rsc)/./app/meals/page.tsx\")), \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/meals/loading.tsx */ \"(rsc)/./app/meals/loading.tsx\")), \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\")), \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/meals/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/meals/page\",\n        pathname: \"/meals\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmeals%2Fpage&page=%2Fmeals%2Fpage&appPaths=%2Fmeals%2Fpage&pagePath=private-next-app-dir%2Fmeals%2Fpage.tsx&appDir=D%3A%5Cgithub%5CMealMate%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5CMealMate%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Navigation.tsx */ \"(ssr)/./components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Capp%5C%5Cmeals%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Capp%5C%5Cmeals%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/meals/page.tsx */ \"(ssr)/./app/meals/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUNNZWFsTWF0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDbWVhbHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0pBQXlGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/MTZhMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFxNZWFsTWF0ZVxcXFxmcm9udGVuZFxcXFxhcHBcXFxcbWVhbHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Capp%5C%5Cmeals%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5CMealMate%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/meals/page.tsx":
/*!****************************!*\
  !*** ./app/meals/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MealsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Search,ShoppingCart,SlidersHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Search,ShoppingCart,SlidersHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Search,ShoppingCart,SlidersHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Search,ShoppingCart,SlidersHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"all\",\n    \"breakfast\",\n    \"lunch\",\n    \"dinner\",\n    \"snacks\",\n    \"beverage\"\n];\nfunction MealsPage() {\n    const { token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [meals, setMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMeals, setFilteredMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        min: 0,\n        max: 1000\n    });\n    const [likedMeals, setLikedMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cartItems, setCartItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMeals();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMeals();\n    }, [\n        meals,\n        searchQuery,\n        selectedCategory,\n        priceRange\n    ]);\n    const fetchMeals = async ()=>{\n        try {\n            const response = await fetch(`${API_BASE_URL}/meals`);\n            if (response.ok) {\n                const data = await response.json();\n                setMeals(data.data);\n                setFilteredMeals(data.data);\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch meals. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMeals = ()=>{\n        const filtered = meals.filter((meal)=>{\n            const matchesSearch = meal.name.toLowerCase().includes(searchQuery.toLowerCase()) || meal.description.toLowerCase().includes(searchQuery.toLowerCase()) || meal.provider.name.toLowerCase().includes(searchQuery.toLowerCase());\n            const matchesCategory = selectedCategory === \"all\" || meal.category === selectedCategory;\n            const matchesPrice = meal.price >= priceRange.min && meal.price <= priceRange.max;\n            return matchesSearch && matchesCategory && matchesPrice && meal.availability;\n        });\n        setFilteredMeals(filtered);\n    };\n    const toggleLike = (mealId)=>{\n        setLikedMeals((prev)=>prev.includes(mealId) ? prev.filter((id)=>id !== mealId) : [\n                ...prev,\n                mealId\n            ]);\n    };\n    const addToCart = (mealId)=>{\n        setCartItems((prev)=>[\n                ...prev,\n                mealId\n            ]);\n        toast({\n            title: \"Added to cart\",\n            description: \"Meal has been added to your cart.\"\n        });\n    };\n    const orderMeal = async (meal)=>{\n        if (!token) {\n            toast({\n                title: \"Authentication required\",\n                description: \"Please login to place an order.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await fetch(`${API_BASE_URL}/orders`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    meal: meal._id,\n                    quantity: 1,\n                    deliveryAddress: \"Default address\",\n                    deliveryDate: new Date(Date.now() + 24 * 60 * 60 * 1000),\n                    specialInstructions: \"\"\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Order placed successfully!\",\n                    description: `Your order for ${meal.name} has been placed.`\n                });\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to place order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Order failed\",\n                description: error instanceof Error ? error.message : \"Failed to place order. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Browse Meals\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Discover delicious homemade meals from local chefs\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"text\",\n                                                placeholder: \"Search meals, cuisines, or chefs...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"pl-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: selectedCategory === category ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedCategory(category),\n                                            className: \"capitalize\",\n                                            children: category\n                                        }, category, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"number\",\n                                            placeholder: \"Min\",\n                                            value: priceRange.min,\n                                            onChange: (e)=>setPriceRange((prev)=>({\n                                                        ...prev,\n                                                        min: Number(e.target.value)\n                                                    })),\n                                            className: \"w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"number\",\n                                            placeholder: \"Max\",\n                                            value: priceRange.max,\n                                            onChange: (e)=>setPriceRange((prev)=>({\n                                                        ...prev,\n                                                        max: Number(e.target.value)\n                                                    })),\n                                            className: \"w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Showing \",\n                                filteredMeals.length,\n                                \" of \",\n                                meals.length,\n                                \" meals\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            children: filteredMeals.map((meal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -5,\n                                        scale: 1.02\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-white h-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-6xl\",\n                                                            children: \"\\uD83C\\uDF7D️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                        className: \"absolute top-4 right-4 p-2 rounded-full bg-white/90 backdrop-blur-sm shadow-lg\",\n                                                        onClick: ()=>toggleLike(meal._id),\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: `w-5 h-5 transition-colors duration-200 ${likedMeals.includes(meal._id) ? \"text-red-500 fill-red-500\" : \"text-gray-600\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"absolute top-4 left-4 bg-orange-500 text-white capitalize\",\n                                                        children: meal.category\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6 flex-1 flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 line-clamp-1 flex-1 mr-2\",\n                                                                children: meal.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold text-orange-600 whitespace-nowrap\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    meal.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4 line-clamp-2 flex-1\",\n                                                        children: meal.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-auto\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"by \",\n                                                                        meal.provider.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                        whileHover: {\n                                                                            scale: 1.05\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.95\n                                                                        },\n                                                                        className: \"flex-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: ()=>addToCart(meal._id),\n                                                                            variant: \"outline\",\n                                                                            className: \"w-full\",\n                                                                            size: \"sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"w-4 h-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                                    lineNumber: 283,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Add to Cart\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                        whileHover: {\n                                                                            scale: 1.05\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.95\n                                                                        },\n                                                                        className: \"flex-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: ()=>orderMeal(meal),\n                                                                            className: \"bg-orange-500 hover:bg-orange-600 text-white w-full\",\n                                                                            size: \"sm\",\n                                                                            children: \"Order Now\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, this)\n                                }, meal._id, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 17\n                                }, this))\n                        }, `${selectedCategory}-${searchQuery}`, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    filteredMeals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"text-center py-16\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.8\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83D\\uDD0D\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"No meals found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"Try adjusting your search criteria or browse different categories\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setSelectedCategory(\"all\");\n                                    setPriceRange({\n                                        min: 0,\n                                        max: 1000\n                                    });\n                                },\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: cartItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"fixed bottom-6 right-6 bg-orange-500 text-white p-4 rounded-full shadow-lg z-50\",\n                            initial: {\n                                scale: 0,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1\n                            },\n                            exit: {\n                                scale: 0,\n                                opacity: 0\n                            },\n                            whileHover: {\n                                scale: 1.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Search_ShoppingCart_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: cartItems.length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\meals\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/meals/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navigation.tsx":
/*!***********************************!*\
  !*** ./components/Navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,Home,LogOut,Menu,Package,Search,Settings,ShoppingCart,User,Utensils,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction Navigation() {\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getNavItems = ()=>{\n        const baseItems = [\n            {\n                href: \"/\",\n                label: \"Home\",\n                icon: _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            },\n            {\n                href: \"/meals\",\n                label: \"Browse Meals\",\n                icon: _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            }\n        ];\n        if (user) {\n            baseItems.push({\n                href: \"/orders\",\n                label: \"Orders\",\n                icon: _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            });\n            if (user.role === \"provider\") {\n                baseItems.push({\n                    href: \"/chef/meals\",\n                    label: \"My Meals\",\n                    icon: _barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                });\n            }\n        }\n        return baseItems;\n    };\n    const navItems = getNavItems();\n    const getDashboardLink = ()=>{\n        if (!user) return \"/auth/login\";\n        switch(user.role){\n            case \"admin\":\n                return \"/admin\";\n            case \"provider\":\n                return \"/chef\";\n            case \"customer\":\n            default:\n                return \"/dashboard\";\n        }\n    };\n    const getDashboardLabel = ()=>{\n        if (!user) return \"Dashboard\";\n        switch(user.role){\n            case \"admin\":\n                return \"Admin Panel\";\n            case \"provider\":\n                return \"Chef Dashboard\";\n            case \"customer\":\n            default:\n                return \"Dashboard\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    whileHover: {\n                                        rotate: 360\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"bg-orange-500 p-2 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"MealMate\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `flex items-center space-x-1 px-3 py-2 rounded-md transition-colors duration-200 ${pathname === item.href ? \"text-orange-600 bg-orange-50\" : \"text-gray-700 hover:text-orange-600 hover:bg-orange-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.href, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/cart\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-2 -right-2 bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"relative h-8 w-8 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                        className: \"h-8 w-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                src: user.profilePhoto || \"/placeholder.svg\",\n                                                                alt: user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                children: user.name.charAt(0).toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                                className: \"w-56\",\n                                                align: \"end\",\n                                                forceMount: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-start gap-2 p-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col space-y-1 leading-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"w-[200px] truncate text-sm text-muted-foreground\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: getDashboardLink(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getDashboardLabel()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/profile\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Profile Settings\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                        onClick: logout,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Log out\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 33\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 61\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.AnimatePresence, {\n                    children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        className: \"md:hidden border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4 space-y-2\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-md transition-colors duration-200 ${pathname === item.href ? \"text-orange-600 bg-orange-50\" : \"text-gray-700 hover:text-orange-600 hover:bg-orange-50\"}`,\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this)),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-200 pt-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: getDashboardLink(),\n                                                className: \"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:text-orange-600 hover:bg-orange-50 rounded-md\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getDashboardLabel()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/profile\",\n                                                className: \"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:text-orange-600 hover:bg-orange-50 rounded-md\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    logout();\n                                                    setMobileMenuOpen(false);\n                                                },\n                                                className: \"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:text-orange-600 hover:bg-orange-50 rounded-md w-full text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_Home_LogOut_Menu_Package_Search_Settings_ShoppingCart_User_Utensils_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Logout\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-2 mt-2 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/auth/login\",\n                                            className: \"block px-4 py-2 text-gray-700 hover:text-orange-600 hover:bg-orange-50 rounded-md\",\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/auth/register\",\n                                            className: \"block px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600\",\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\Navigation.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_1__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 25,\n        columnNumber: 10\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 61,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 80,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 105,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 96,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 127,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 119,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 143,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 155,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 160,\n        columnNumber: 10\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEI7QUFDd0M7QUFDWjtBQUMxQjtBQUVoQyxNQUFNTSxlQUFlTCwrREFBMEI7QUFFL0MsTUFBTU8sc0JBQXNCUCxrRUFBNkI7QUFFekQsTUFBTVMsb0JBQW9CVCxnRUFBMkI7QUFFckQsTUFBTVcscUJBQXFCWCxpRUFBNEI7QUFFdkQsTUFBTWEsa0JBQWtCYiw4REFBeUI7QUFFakQsTUFBTWUseUJBQXlCZixxRUFBZ0M7QUFFL0QsTUFBTWlCLHVDQUF5QmxCLDZDQUFnQixDQUs3QyxDQUFDLEVBQUVvQixTQUFTLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzNDLDhEQUFDdkIscUVBQWdDO1FBQy9CdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gsd0lBQ0FnQixTQUFTLFFBQ1REO1FBRUQsR0FBR0csS0FBSzs7WUFFUkQ7MEJBQ0QsOERBQUNuQixxR0FBWUE7Z0JBQUNpQixXQUFVOzs7Ozs7Ozs7Ozs7QUFHNUJGLHVCQUF1QlEsV0FBVyxHQUFHekIscUVBQWdDLENBQUN5QixXQUFXO0FBRWpGLE1BQU1DLHVDQUF5QjNCLDZDQUFnQixDQUc3QyxDQUFDLEVBQUVvQixTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQyxvQkFDMUIsOERBQUN2QixxRUFBZ0M7UUFDL0J1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FDWCx5YkFDQWU7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHYkksdUJBQXVCRCxXQUFXLEdBQUd6QixxRUFBZ0MsQ0FBQ3lCLFdBQVc7QUFFakYsTUFBTUcsb0NBQXNCN0IsNkNBQWdCLENBRzFDLENBQUMsRUFBRW9CLFNBQVMsRUFBRVUsYUFBYSxDQUFDLEVBQUUsR0FBR1AsT0FBTyxFQUFFQyxvQkFDMUMsOERBQUN2QixpRUFBNEI7a0JBQzNCLDRFQUFDQSxrRUFBNkI7WUFDNUJ1QixLQUFLQTtZQUNMTSxZQUFZQTtZQUNaVixXQUFXZiw4Q0FBRUEsQ0FDWCx5YkFDQWU7WUFFRCxHQUFHRyxLQUFLOzs7Ozs7Ozs7OztBQUlmTSxvQkFBb0JILFdBQVcsR0FBR3pCLGtFQUE2QixDQUFDeUIsV0FBVztBQUUzRSxNQUFNTSxpQ0FBbUJoQyw2Q0FBZ0IsQ0FLdkMsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFQyxLQUFLLEVBQUUsR0FBR0UsT0FBTyxFQUFFQyxvQkFDakMsOERBQUN2QiwrREFBMEI7UUFDekJ1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FDWCxtT0FDQWdCLFNBQVMsUUFDVEQ7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHYlMsaUJBQWlCTixXQUFXLEdBQUd6QiwrREFBMEIsQ0FBQ3lCLFdBQVc7QUFFckUsTUFBTVEseUNBQTJCbEMsNkNBQWdCLENBRy9DLENBQUMsRUFBRW9CLFNBQVMsRUFBRUUsUUFBUSxFQUFFYSxPQUFPLEVBQUUsR0FBR1osT0FBTyxFQUFFQyxvQkFDN0MsOERBQUN2Qix1RUFBa0M7UUFDakN1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FDWCx3T0FDQWU7UUFFRmUsU0FBU0E7UUFDUixHQUFHWixLQUFLOzswQkFFVCw4REFBQ2M7Z0JBQUtqQixXQUFVOzBCQUNkLDRFQUFDbkIsd0VBQW1DOzhCQUNsQyw0RUFBQ0MscUdBQUtBO3dCQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztZQUdwQkU7Ozs7Ozs7QUFHTFkseUJBQXlCUixXQUFXLEdBQUd6Qix1RUFBa0MsQ0FBQ3lCLFdBQVc7QUFFckYsTUFBTWEsc0NBQXdCdkMsNkNBQWdCLENBRzVDLENBQUMsRUFBRW9CLFNBQVMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3BDLDhEQUFDdkIsb0VBQStCO1FBQzlCdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gsd09BQ0FlO1FBRUQsR0FBR0csS0FBSzs7MEJBRVQsOERBQUNjO2dCQUFLakIsV0FBVTswQkFDZCw0RUFBQ25CLHdFQUFtQzs4QkFDbEMsNEVBQUNHLHFHQUFNQTt3QkFBQ2dCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFHckJFOzs7Ozs7O0FBR0xpQixzQkFBc0JiLFdBQVcsR0FBR3pCLG9FQUErQixDQUFDeUIsV0FBVztBQUUvRSxNQUFNZSxrQ0FBb0J6Qyw2Q0FBZ0IsQ0FLeEMsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFQyxLQUFLLEVBQUUsR0FBR0UsT0FBTyxFQUFFQyxvQkFDakMsOERBQUN2QixnRUFBMkI7UUFDMUJ1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FBQyxxQ0FBcUNnQixTQUFTLFFBQVFEO1FBQ25FLEdBQUdHLEtBQUs7Ozs7OztBQUdia0Isa0JBQWtCZixXQUFXLEdBQUd6QixnRUFBMkIsQ0FBQ3lCLFdBQVc7QUFFdkUsTUFBTWlCLHNDQUF3QjNDLDZDQUFnQixDQUc1QyxDQUFDLEVBQUVvQixTQUFTLEVBQUUsR0FBR0csT0FBTyxFQUFFQyxvQkFDMUIsOERBQUN2QixvRUFBK0I7UUFBQ3VCLEtBQUtBO1FBQUtKLFdBQVdmLDhDQUFFQSxDQUFDLDRCQUE0QmU7UUFBYSxHQUFHRyxLQUFLOzs7Ozs7QUFFNUdvQixzQkFBc0JqQixXQUFXLEdBQUd6QixvRUFBK0IsQ0FBQ3lCLFdBQVc7QUFFL0UsTUFBTW1CLHVCQUF1QixDQUFDLEVBQUV6QixTQUFTLEVBQUUsR0FBR0csT0FBOEM7SUFDMUYscUJBQU8sOERBQUNjO1FBQUtqQixXQUFXZiw4Q0FBRUEsQ0FBQyw4Q0FBOENlO1FBQWEsR0FBR0csS0FBSzs7Ozs7O0FBQ2hHO0FBQ0FzQixxQkFBcUJuQixXQUFXLEdBQUc7QUFrQmxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudS50c3g/MDI0YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgRHJvcGRvd25NZW51UHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtZHJvcGRvd24tbWVudVwiXG5pbXBvcnQgeyBDaGVjaywgQ2hldnJvblJpZ2h0LCBDaXJjbGUgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgRHJvcGRvd25NZW51ID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlJvb3RcblxuY29uc3QgRHJvcGRvd25NZW51VHJpZ2dlciA9IERyb3Bkb3duTWVudVByaW1pdGl2ZS5UcmlnZ2VyXG5cbmNvbnN0IERyb3Bkb3duTWVudUdyb3VwID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLkdyb3VwXG5cbmNvbnN0IERyb3Bkb3duTWVudVBvcnRhbCA9IERyb3Bkb3duTWVudVByaW1pdGl2ZS5Qb3J0YWxcblxuY29uc3QgRHJvcGRvd25NZW51U3ViID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YlxuXG5jb25zdCBEcm9wZG93bk1lbnVSYWRpb0dyb3VwID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvR3JvdXBcblxuY29uc3QgRHJvcGRvd25NZW51U3ViVHJpZ2dlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJUcmlnZ2VyPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViVHJpZ2dlcj4gJiB7XG4gICAgaW5zZXQ/OiBib29sZWFuXG4gIH1cbj4oKHsgY2xhc3NOYW1lLCBpbnNldCwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YlRyaWdnZXJcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJmbGV4IGN1cnNvci1kZWZhdWx0IHNlbGVjdC1ub25lIGl0ZW1zLWNlbnRlciByb3VuZGVkLXNtIHB4LTIgcHktMS41IHRleHQtc20gb3V0bGluZS1ub25lIGZvY3VzOmJnLWFjY2VudCBkYXRhLVtzdGF0ZT1vcGVuXTpiZy1hY2NlbnRcIixcbiAgICAgIGluc2V0ICYmIFwicGwtOFwiLFxuICAgICAgY2xhc3NOYW1lLFxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICA+XG4gICAge2NoaWxkcmVufVxuICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwibWwtYXV0byBoLTQgdy00XCIgLz5cbiAgPC9Ecm9wZG93bk1lbnVQcmltaXRpdmUuU3ViVHJpZ2dlcj5cbikpXG5Ecm9wZG93bk1lbnVTdWJUcmlnZ2VyLmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YlRyaWdnZXIuZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51U3ViQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJDb250ZW50PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViQ29udGVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJDb250ZW50XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwiei01MCBtaW4tdy1bOHJlbV0gb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtbWQgYm9yZGVyIGJnLXBvcG92ZXIgcC0xIHRleHQtcG9wb3Zlci1mb3JlZ3JvdW5kIHNoYWRvdy1sZyBkYXRhLVtzdGF0ZT1vcGVuXTphbmltYXRlLWluIGRhdGEtW3N0YXRlPWNsb3NlZF06YW5pbWF0ZS1vdXQgZGF0YS1bc3RhdGU9Y2xvc2VkXTpmYWRlLW91dC0wIGRhdGEtW3N0YXRlPW9wZW5dOmZhZGUtaW4tMCBkYXRhLVtzdGF0ZT1jbG9zZWRdOnpvb20tb3V0LTk1IGRhdGEtW3N0YXRlPW9wZW5dOnpvb20taW4tOTUgZGF0YS1bc2lkZT1ib3R0b21dOnNsaWRlLWluLWZyb20tdG9wLTIgZGF0YS1bc2lkZT1sZWZ0XTpzbGlkZS1pbi1mcm9tLXJpZ2h0LTIgZGF0YS1bc2lkZT1yaWdodF06c2xpZGUtaW4tZnJvbS1sZWZ0LTIgZGF0YS1bc2lkZT10b3BdOnNsaWRlLWluLWZyb20tYm90dG9tLTJcIixcbiAgICAgIGNsYXNzTmFtZSxcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5Ecm9wZG93bk1lbnVTdWJDb250ZW50LmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YkNvbnRlbnQuZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51Q29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5Db250ZW50PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ29udGVudD5cbj4oKHsgY2xhc3NOYW1lLCBzaWRlT2Zmc2V0ID0gNCwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuUG9ydGFsPlxuICAgIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuQ29udGVudFxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBzaWRlT2Zmc2V0PXtzaWRlT2Zmc2V0fVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJ6LTUwIG1pbi13LVs4cmVtXSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1tZCBib3JkZXIgYmctcG9wb3ZlciBwLTEgdGV4dC1wb3BvdmVyLWZvcmVncm91bmQgc2hhZG93LW1kIGRhdGEtW3N0YXRlPW9wZW5dOmFuaW1hdGUtaW4gZGF0YS1bc3RhdGU9Y2xvc2VkXTphbmltYXRlLW91dCBkYXRhLVtzdGF0ZT1jbG9zZWRdOmZhZGUtb3V0LTAgZGF0YS1bc3RhdGU9b3Blbl06ZmFkZS1pbi0wIGRhdGEtW3N0YXRlPWNsb3NlZF06em9vbS1vdXQtOTUgZGF0YS1bc3RhdGU9b3Blbl06em9vbS1pbi05NSBkYXRhLVtzaWRlPWJvdHRvbV06c2xpZGUtaW4tZnJvbS10b3AtMiBkYXRhLVtzaWRlPWxlZnRdOnNsaWRlLWluLWZyb20tcmlnaHQtMiBkYXRhLVtzaWRlPXJpZ2h0XTpzbGlkZS1pbi1mcm9tLWxlZnQtMiBkYXRhLVtzaWRlPXRvcF06c2xpZGUtaW4tZnJvbS1ib3R0b20tMlwiLFxuICAgICAgICBjbGFzc05hbWUsXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLlBvcnRhbD5cbikpXG5Ecm9wZG93bk1lbnVDb250ZW50LmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLkNvbnRlbnQuZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51SXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbT4gJiB7XG4gICAgaW5zZXQ/OiBib29sZWFuXG4gIH1cbj4oKHsgY2xhc3NOYW1lLCBpbnNldCwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbVxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGZsZXggY3Vyc29yLWRlZmF1bHQgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIHJvdW5kZWQtc20gcHgtMiBweS0xLjUgdGV4dC1zbSBvdXRsaW5lLW5vbmUgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXM6YmctYWNjZW50IGZvY3VzOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGF0YS1bZGlzYWJsZWRdOnBvaW50ZXItZXZlbnRzLW5vbmUgZGF0YS1bZGlzYWJsZWRdOm9wYWNpdHktNTBcIixcbiAgICAgIGluc2V0ICYmIFwicGwtOFwiLFxuICAgICAgY2xhc3NOYW1lLFxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkRyb3Bkb3duTWVudUl0ZW0uZGlzcGxheU5hbWUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbS5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ2hlY2tib3hJdGVtPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ2hlY2tib3hJdGVtPlxuPigoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCBjaGVja2VkLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPERyb3Bkb3duTWVudVByaW1pdGl2ZS5DaGVja2JveEl0ZW1cbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJyZWxhdGl2ZSBmbGV4IGN1cnNvci1kZWZhdWx0IHNlbGVjdC1ub25lIGl0ZW1zLWNlbnRlciByb3VuZGVkLXNtIHB5LTEuNSBwbC04IHByLTIgdGV4dC1zbSBvdXRsaW5lLW5vbmUgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXM6YmctYWNjZW50IGZvY3VzOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGF0YS1bZGlzYWJsZWRdOnBvaW50ZXItZXZlbnRzLW5vbmUgZGF0YS1bZGlzYWJsZWRdOm9wYWNpdHktNTBcIixcbiAgICAgIGNsYXNzTmFtZSxcbiAgICApfVxuICAgIGNoZWNrZWQ9e2NoZWNrZWR9XG4gICAgey4uLnByb3BzfVxuICA+XG4gICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0yIGZsZXggaC0zLjUgdy0zLjUgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkl0ZW1JbmRpY2F0b3I+XG4gICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLkl0ZW1JbmRpY2F0b3I+XG4gICAgPC9zcGFuPlxuICAgIHtjaGlsZHJlbn1cbiAgPC9Ecm9wZG93bk1lbnVQcmltaXRpdmUuQ2hlY2tib3hJdGVtPlxuKSlcbkRyb3Bkb3duTWVudUNoZWNrYm94SXRlbS5kaXNwbGF5TmFtZSA9IERyb3Bkb3duTWVudVByaW1pdGl2ZS5DaGVja2JveEl0ZW0uZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51UmFkaW9JdGVtID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbT4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbT5cbj4oKHsgY2xhc3NOYW1lLCBjaGlsZHJlbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuUmFkaW9JdGVtXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicmVsYXRpdmUgZmxleCBjdXJzb3ItZGVmYXVsdCBzZWxlY3Qtbm9uZSBpdGVtcy1jZW50ZXIgcm91bmRlZC1zbSBweS0xLjUgcGwtOCBwci0yIHRleHQtc20gb3V0bGluZS1ub25lIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzOmJnLWFjY2VudCBmb2N1czp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIGRhdGEtW2Rpc2FibGVkXTpwb2ludGVyLWV2ZW50cy1ub25lIGRhdGEtW2Rpc2FibGVkXTpvcGFjaXR5LTUwXCIsXG4gICAgICBjbGFzc05hbWUsXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gID5cbiAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTIgZmxleCBoLTMuNSB3LTMuNSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbUluZGljYXRvcj5cbiAgICAgICAgPENpcmNsZSBjbGFzc05hbWU9XCJoLTIgdy0yIGZpbGwtY3VycmVudFwiIC8+XG4gICAgICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtSW5kaWNhdG9yPlxuICAgIDwvc3Bhbj5cbiAgICB7Y2hpbGRyZW59XG4gIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbT5cbikpXG5Ecm9wZG93bk1lbnVSYWRpb0l0ZW0uZGlzcGxheU5hbWUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuUmFkaW9JdGVtLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudUxhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkxhYmVsPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuTGFiZWw+ICYge1xuICAgIGluc2V0PzogYm9vbGVhblxuICB9XG4+KCh7IGNsYXNzTmFtZSwgaW5zZXQsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkxhYmVsXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInB4LTIgcHktMS41IHRleHQtc20gZm9udC1zZW1pYm9sZFwiLCBpbnNldCAmJiBcInBsLThcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuRHJvcGRvd25NZW51TGFiZWwuZGlzcGxheU5hbWUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuTGFiZWwuZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51U2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlNlcGFyYXRvcj4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlNlcGFyYXRvcj5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPERyb3Bkb3duTWVudVByaW1pdGl2ZS5TZXBhcmF0b3IgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCItbXgtMSBteS0xIGgtcHggYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkRyb3Bkb3duTWVudVNlcGFyYXRvci5kaXNwbGF5TmFtZSA9IERyb3Bkb3duTWVudVByaW1pdGl2ZS5TZXBhcmF0b3IuZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51U2hvcnRjdXQgPSAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxTcGFuRWxlbWVudD4pID0+IHtcbiAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT17Y24oXCJtbC1hdXRvIHRleHQteHMgdHJhY2tpbmctd2lkZXN0IG9wYWNpdHktNjBcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxufVxuRHJvcGRvd25NZW51U2hvcnRjdXQuZGlzcGxheU5hbWUgPSBcIkRyb3Bkb3duTWVudVNob3J0Y3V0XCJcblxuZXhwb3J0IHtcbiAgRHJvcGRvd25NZW51LFxuICBEcm9wZG93bk1lbnVUcmlnZ2VyLFxuICBEcm9wZG93bk1lbnVDb250ZW50LFxuICBEcm9wZG93bk1lbnVJdGVtLFxuICBEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0sXG4gIERyb3Bkb3duTWVudVJhZGlvSXRlbSxcbiAgRHJvcGRvd25NZW51TGFiZWwsXG4gIERyb3Bkb3duTWVudVNlcGFyYXRvcixcbiAgRHJvcGRvd25NZW51U2hvcnRjdXQsXG4gIERyb3Bkb3duTWVudUdyb3VwLFxuICBEcm9wZG93bk1lbnVQb3J0YWwsXG4gIERyb3Bkb3duTWVudVN1YixcbiAgRHJvcGRvd25NZW51U3ViQ29udGVudCxcbiAgRHJvcGRvd25NZW51U3ViVHJpZ2dlcixcbiAgRHJvcGRvd25NZW51UmFkaW9Hcm91cCxcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkRyb3Bkb3duTWVudVByaW1pdGl2ZSIsIkNoZWNrIiwiQ2hldnJvblJpZ2h0IiwiQ2lyY2xlIiwiY24iLCJEcm9wZG93bk1lbnUiLCJSb290IiwiRHJvcGRvd25NZW51VHJpZ2dlciIsIlRyaWdnZXIiLCJEcm9wZG93bk1lbnVHcm91cCIsIkdyb3VwIiwiRHJvcGRvd25NZW51UG9ydGFsIiwiUG9ydGFsIiwiRHJvcGRvd25NZW51U3ViIiwiU3ViIiwiRHJvcGRvd25NZW51UmFkaW9Hcm91cCIsIlJhZGlvR3JvdXAiLCJEcm9wZG93bk1lbnVTdWJUcmlnZ2VyIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImluc2V0IiwiY2hpbGRyZW4iLCJwcm9wcyIsInJlZiIsIlN1YlRyaWdnZXIiLCJkaXNwbGF5TmFtZSIsIkRyb3Bkb3duTWVudVN1YkNvbnRlbnQiLCJTdWJDb250ZW50IiwiRHJvcGRvd25NZW51Q29udGVudCIsInNpZGVPZmZzZXQiLCJDb250ZW50IiwiRHJvcGRvd25NZW51SXRlbSIsIkl0ZW0iLCJEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0iLCJjaGVja2VkIiwiQ2hlY2tib3hJdGVtIiwic3BhbiIsIkl0ZW1JbmRpY2F0b3IiLCJEcm9wZG93bk1lbnVSYWRpb0l0ZW0iLCJSYWRpb0l0ZW0iLCJEcm9wZG93bk1lbnVMYWJlbCIsIkxhYmVsIiwiRHJvcGRvd25NZW51U2VwYXJhdG9yIiwiU2VwYXJhdG9yIiwiRHJvcGRvd25NZW51U2hvcnRjdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 45,\n        columnNumber: 10\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 77,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(({ id, title, description, action, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 23\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const storedToken = localStorage.getItem(\"token\");\n        if (storedToken) {\n            setToken(storedToken);\n            fetchCurrentUser(storedToken);\n        } else {\n            setLoading(false);\n        }\n    }, []);\n    const fetchCurrentUser = async (authToken)=>{\n        try {\n            const response = await fetch(`${API_BASE_URL}/auth/me`, {\n                headers: {\n                    Authorization: `Bearer ${authToken}`\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUser(data.data);\n            } else {\n                localStorage.removeItem(\"token\");\n                setToken(null);\n            }\n        } catch (error) {\n            console.error(\"Error fetching user:\", error);\n            localStorage.removeItem(\"token\");\n            setToken(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        const response = await fetch(`${API_BASE_URL}/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || \"Login failed\");\n        }\n        const data = await response.json();\n        const authToken = data.token;\n        setToken(authToken);\n        localStorage.setItem(\"token\", authToken);\n        await fetchCurrentUser(authToken);\n    };\n    const register = async (userData)=>{\n        const response = await fetch(`${API_BASE_URL}/auth/register`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.error || \"Registration failed\");\n        }\n        const data = await response.json();\n        const authToken = data.token;\n        setToken(authToken);\n        localStorage.setItem(\"token\", authToken);\n        await fetchCurrentUser(authToken);\n    };\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        localStorage.removeItem(\"token\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            login,\n            register,\n            logout,\n            loading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e738fbc2779f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vYXBwL2dsb2JhbHMuY3NzPzEzMTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNzM4ZmJjMjc3OWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./components/Navigation.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"MealMate - Homemade Food Delivery\",\n    description: \"Connect with local home chefs and enjoy fresh, homemade meals delivered to your doorstep\",\n    generator: \"v0.dev\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvLi9hcHAvbG9hZGluZy50c3g/YzUyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/meals/loading.tsx":
/*!*******************************!*\
  !*** ./app/meals/loading.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbWVhbHMvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvLi9hcHAvbWVhbHMvbG9hZGluZy50c3g/NjVlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/meals/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/meals/page.tsx":
/*!****************************!*\
  !*** ./app/meals/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\MealMate\frontend\app\meals\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Navigation.tsx":
/*!***********************************!*\
  !*** ./components/Navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\MealMate\frontend\components\Navigation.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\MealMate\frontend\components\ui\toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\MealMate\frontend\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\github\MealMate\frontend\contexts\AuthContext.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/@floating-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/motion-utils","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-sync-external-store","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmeals%2Fpage&page=%2Fmeals%2Fpage&appPaths=%2Fmeals%2Fpage&pagePath=private-next-app-dir%2Fmeals%2Fpage.tsx&appDir=D%3A%5Cgithub%5CMealMate%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5CMealMate%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();