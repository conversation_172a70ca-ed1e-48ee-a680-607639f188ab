"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Star,TrendingUp,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    var _statusConfig_selectedOrder_status, _statusConfig_selectedOrder_status1, _statusConfig_selectedOrder_status2;\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"pending\" : \"active\");\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Remove auto-refresh - not needed for this implementation\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                // Update the order in local state immediately\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            status: newStatus\n                        } : order));\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n            // Don't call fetchOrders() to avoid page refresh and tab reset\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                // Update the order status in local state\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            status: \"cancelled\"\n                        } : order));\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n            // Don't call fetchOrders() to avoid page refresh\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const handleViewDetails = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 328,\n            columnNumber: 7\n        }, this);\n    }\n    // Render different interfaces based on user role\n    if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefOrdersInterface, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 336,\n            columnNumber: 12\n        }, this);\n    } else {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrdersInterface, {}, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 338,\n            columnNumber: 12\n        }, this);\n    }\n    // Chef Orders Interface\n    function ChefOrdersInterface() {\n        const pendingOrders = orders.filter((order)=>order.status === \"pending\");\n        const activeOrders = orders.filter((order)=>[\n                \"confirmed\",\n                \"preparing\",\n                \"ready\"\n            ].includes(order.status));\n        const completedOrders = orders.filter((order)=>[\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-8 h-8 text-orange-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Order Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Manage incoming orders and track your business\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Pending Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-yellow-600\",\n                                                            children: pendingOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-8 h-8 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: activeOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: completedOrders.filter((order)=>new Date(order.createdAt).toDateString() === new Date().toDateString()).length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Today's Revenue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: [\n                                                                \"₹\",\n                                                                completedOrders.filter((order)=>new Date(order.createdAt).toDateString() === new Date().toDateString() && order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            defaultValue: \"pending\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"pending\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pending (\",\n                                                pendingOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"active\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active (\",\n                                                activeOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"completed\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Completed (\",\n                                                completedOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"pending\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefPendingOrders, {\n                                        orders: pendingOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"active\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefActiveOrders, {\n                                        orders: activeOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"completed\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefCompletedOrders, {\n                                        orders: completedOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Orders Interface\n    function CustomerOrdersInterface() {\n        const activeOrders = orders.filter((order)=>![\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        const pastOrders = orders.filter((order)=>[\n                \"delivered\",\n                \"cancelled\"\n            ].includes(order.status));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-8 h-8 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"My Orders\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Track your meal orders and order history\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: activeOrders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: orders.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Spent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: [\n                                                                \"₹\",\n                                                                orders.filter((order)=>order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            defaultValue: \"active\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"active\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active Orders (\",\n                                                activeOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"history\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Order History (\",\n                                                pastOrders.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"active\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerActiveOrders, {\n                                        orders: activeOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"history\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrderHistory, {\n                                        orders: pastOrders\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 482,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 481,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Pending Orders Component\n    function ChefPendingOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No pending orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"New orders will appear here for your approval\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 585,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 border-l-yellow-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: order.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        order._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"bg-yellow-500 text-white\",\n                                            children: \"New Order\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Customer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Delivery Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm bg-gray-50 p-2 rounded\",\n                                                children: order.deliveryAddress\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-1\",\n                                                children: \"Special Instructions:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm bg-yellow-50 p-2 rounded border border-yellow-200\",\n                                                children: order.specialInstructions\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                children: updatingOrders.has(order._id) ? \"Accepting...\" : \"Accept Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>updateOrderStatus(order._id, \"cancelled\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"text-red-600 border-red-600 hover:bg-red-50\",\n                                                children: updatingOrders.has(order._id) ? \"Declining...\" : \"Decline\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 13\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 596,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Active Orders Component\n    function ChefActiveOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No active orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Accepted orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 692,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(order.status === \"confirmed\" ? \"border-l-blue-500\" : order.status === \"preparing\" ? \"border-l-purple-500\" : \"border-l-orange-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                order.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Delivery Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Order Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.createdAt).toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            order.status === \"confirmed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-purple-600 hover:bg-purple-700\",\n                                                children: updatingOrders.has(order._id) ? \"Starting...\" : \"Start Preparing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 23\n                                            }, this),\n                                            order.status === \"preparing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: updatingOrders.has(order._id) ? \"Marking...\" : \"Mark Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 23\n                                            }, this),\n                                            order.status === \"ready\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                                                disabled: updatingOrders.has(order._id),\n                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                children: updatingOrders.has(order._id) ? \"Delivering...\" : \"Mark Delivered\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 800,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 709,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 703,\n            columnNumber: 7\n        }, this);\n    }\n    // Chef Completed Orders Component\n    function ChefCompletedOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No completed orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Completed orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 821,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 818,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 817,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-l-4 \".concat(order.status === \"delivered\" ? \"border-l-green-500\" : \"border-l-red-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                order.user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Earnings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: order.status === \"delivered\" ? \"₹\".concat(order.meal.price * order.quantity) : \"₹0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 872,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Completed Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.createdAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Rating\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Not rated yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>handleViewDetails(order),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 834,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 828,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Active Orders Component\n    function CustomerActiveOrders(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 915,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No active orders\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Your active orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 913,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Chef\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 960,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 966,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 965,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 955,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Order Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    \"pending\",\n                                                    \"confirmed\",\n                                                    \"preparing\",\n                                                    \"ready\",\n                                                    \"delivered\"\n                                                ].map((status, idx)=>{\n                                                    const isActive = [\n                                                        \"pending\",\n                                                        \"confirmed\",\n                                                        \"preparing\",\n                                                        \"ready\",\n                                                        \"delivered\"\n                                                    ].indexOf(order.status) >= idx;\n                                                    const isCurrent = order.status === status;\n                                                    const isCompleted = [\n                                                        \"pending\",\n                                                        \"confirmed\",\n                                                        \"preparing\",\n                                                        \"ready\",\n                                                        \"delivered\"\n                                                    ].indexOf(order.status) > idx;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full border-2 transition-all duration-300 \".concat(isCompleted ? \"bg-green-500 border-green-500\" : isCurrent ? \"bg-orange-500 border-orange-500 animate-pulse\" : isActive ? \"bg-blue-500 border-blue-500\" : \"bg-gray-200 border-gray-300\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            idx < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-0.5 transition-all duration-300 \".concat(isCompleted || isActive && idx < [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) ? \"bg-green-500\" : \"bg-gray-200\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, status, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between mt-1\",\n                                                children: [\n                                                    \"Pending\",\n                                                    \"Confirmed\",\n                                                    \"Preparing\",\n                                                    \"Ready\",\n                                                    \"Delivered\"\n                                                ].map((label, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs transition-all duration-300 \".concat([\n                                                            \"pending\",\n                                                            \"confirmed\",\n                                                            \"preparing\",\n                                                            \"ready\",\n                                                            \"delivered\"\n                                                        ].indexOf(order.status) >= idx ? \"text-gray-700 font-medium\" : \"text-gray-400\"),\n                                                        children: label\n                                                    }, label, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            [\n                                                \"pending\",\n                                                \"confirmed\"\n                                            ].includes(order.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                size: \"sm\",\n                                                onClick: ()=>cancelOrder(order._id),\n                                                disabled: updatingOrders.has(order._id),\n                                                children: updatingOrders.has(order._id) ? \"Cancelling...\" : \"Cancel Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 936,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 930,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 924,\n            columnNumber: 7\n        }, this);\n    }\n    // Customer Order History Component\n    function CustomerOrderHistory(param) {\n        let { orders } = param;\n        if (orders.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1070,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No order history\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1071,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Your completed orders will appear here\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1072,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1069,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1068,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: orders.map((order, index)=>{\n                const statusInfo = statusConfig[order.status];\n                const StatusIcon = (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.icon) || _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: index * 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"opacity-90 hover:opacity-100 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-full \".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1095,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: order.meal.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Order #\",\n                                                                order._id.slice(-8),\n                                                                \" • \",\n                                                                new Date(order.createdAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1098,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color, \" text-white\"),\n                                            children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1105,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1092,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1114,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Chef\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1117,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1115,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1123,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: order.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1124,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Total Paid\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    order.meal.price * order.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Delivery Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 1138,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1112,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            order.status === \"delivered\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-yellow-600 border-yellow-600 hover:bg-yellow-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1150,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Rate Order\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleViewDetails(order),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Star_TrendingUp_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 1159,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"View Details\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 1154,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1111,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 1091,\n                        columnNumber: 15\n                    }, this)\n                }, order._id, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1085,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 1079,\n            columnNumber: 7\n        }, this);\n    }\n    // Main render - choose interface based on user role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChefOrdersInterface, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1175,\n                columnNumber: 36\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerOrdersInterface, {}, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1175,\n                columnNumber: 62\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: showOrderDetails,\n                onOpenChange: setShowOrderDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                    className: \"max-w-2xl max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                children: \"Order Details\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 1181,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 11\n                        }, this),\n                        selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: selectedOrder.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        selectedOrder._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(((_statusConfig_selectedOrder_status = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status === void 0 ? void 0 : _statusConfig_selectedOrder_status.color) || \"bg-gray-500\", \" text-white\"),\n                                            children: ((_statusConfig_selectedOrder_status1 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status1 === void 0 ? void 0 : _statusConfig_selectedOrder_status1.text) || selectedOrder.status\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? selectedOrder.user.name : selectedOrder.provider.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Quantity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1210,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedOrder.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1215,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: [\n                                                                \"₹\",\n                                                                selectedOrder.meal.price * selectedOrder.quantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1216,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Delivery Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1222,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.deliveryDate).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1223,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Order Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1232,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (_statusConfig_selectedOrder_status2 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status2 === void 0 ? void 0 : _statusConfig_selectedOrder_status2.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 1233,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Delivery Address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.deliveryAddress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1241,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 15\n                                }, this),\n                                selectedOrder.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Special Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1247,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.specialInstructions\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 pt-4 border-t\",\n                                    children: [\n                                        getStatusActions(selectedOrder),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowOrderDetails(false),\n                                            className: \"ml-auto\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 1255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 1253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 1185,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 1178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(OrdersPage, \"pjVj0jVGmZUF3WODKeNwxsOqrrA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});