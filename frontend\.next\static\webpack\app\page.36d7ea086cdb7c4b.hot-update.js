"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MealMatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        y: 20,\n        opacity: 0\n    },\n    visible: {\n        y: 0,\n        opacity: 1,\n        transition: {\n            type: \"spring\",\n            stiffness: 100,\n            damping: 10\n        }\n    }\n};\nconst floatingVariants = {\n    animate: {\n        y: [\n            -10,\n            10,\n            -10\n        ],\n        transition: {\n            duration: 3,\n            repeat: Number.POSITIVE_INFINITY,\n            ease: \"easeInOut\"\n        }\n    }\n};\nconst stats = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"Home Chefs\",\n        value: \"500+\",\n        color: \"text-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"Happy Customers\",\n        value: \"10K+\",\n        color: \"text-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"Orders Delivered\",\n        value: \"50K+\",\n        color: \"text-green-500\"\n    }\n];\nfunction MealMatePage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [likedMeals, setLikedMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cartItems, setCartItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meals, setMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    const categories = [\n        \"all\",\n        \"breakfast\",\n        \"lunch\",\n        \"dinner\",\n        \"snacks\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFeaturedMeals();\n    }, []);\n    const fetchFeaturedMeals = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/meals\"));\n            if (response.ok) {\n                const data = await response.json();\n                // Get first 6 available meals for featured section\n                const availableMeals = data.data.filter((meal)=>meal.availability).slice(0, 6);\n                setMeals(availableMeals);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch meals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleLike = (mealId)=>{\n        setLikedMeals((prev)=>prev.includes(mealId) ? prev.filter((id)=>id !== mealId) : [\n                ...prev,\n                mealId\n            ]);\n    };\n    const addToCart = (mealId)=>{\n        setCartItems((prev)=>[\n                ...prev,\n                mealId\n            ]);\n    };\n    const filteredMeals = meals.filter((meal)=>{\n        const matchesSearch = meal.name.toLowerCase().includes(searchQuery.toLowerCase()) || meal.description.toLowerCase().includes(searchQuery.toLowerCase()) || meal.provider.name.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || meal.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.section, {\n                className: \"relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 py-20 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            variants: containerVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"bg-white/20 text-white border-white/30 mb-4\",\n                                            children: \"\\uD83C\\uDF7D️ Homemade with Love\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-orange-100 bg-clip-text text-transparent\",\n                                            children: [\n                                                \"Discover Authentic\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300\",\n                                                    children: \"Home Cooking\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                    variants: itemVariants,\n                                    className: \"text-xl md:text-2xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                    children: \"Connect with local home chefs and enjoy fresh, homemade meals delivered to your doorstep\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-white text-orange-600 hover:bg-orange-50 transform hover:scale-105 transition-all duration-200 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Order Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"border-white text-orange-600 hover:bg-orange-50 hover:text-orange-600 transform hover:scale-105 transition-all duration-200\",\n                                            children: [\n                                                \"Become a Chef\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 right-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        transition: {\n                                            delay: 1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-10 h-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.section, {\n                className: \"py-16 bg-white\",\n                initial: {\n                    opacity: 0,\n                    y: 50\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4 \".concat(stat.color),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-8 h-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.section, {\n                className: \"py-12 bg-gray-50\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"max-w-4xl mx-auto\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900\",\n                                children: \"Find Your Perfect Meal\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search for meals, cuisines, or chefs...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-200\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                variants: itemVariants,\n                                className: \"flex flex-wrap gap-3 justify-center\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: selectedCategory === category ? \"default\" : \"outline\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"capitalize rounded-full transition-all duration-200 \".concat(selectedCategory === category ? \"bg-orange-500 hover:bg-orange-600 transform scale-105\" : \"hover:bg-orange-50 hover:border-orange-300\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.section, {\n                className: \"py-16\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                variants: containerVariants,\n                                initial: \"hidden\",\n                                animate: \"visible\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-full flex justify-center py-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this) : filteredMeals.map((meal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        variants: itemVariants,\n                                        whileHover: {\n                                            y: -10,\n                                            scale: 1.02\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300,\n                                            damping: 20\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white h-full flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-6xl\",\n                                                                children: \"\\uD83C\\uDF7D️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                                            className: \"absolute top-4 right-4 p-2 rounded-full bg-white/90 backdrop-blur-sm shadow-lg\",\n                                                            onClick: ()=>toggleLike(meal._id),\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.9\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 transition-colors duration-200 \".concat(likedMeals.includes(meal._id) ? \"text-red-500 fill-red-500\" : \"text-gray-600\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: \"absolute top-4 left-4 bg-orange-500 text-white capitalize\",\n                                                            children: meal.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6 flex-1 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 line-clamp-1 flex-1 mr-2\",\n                                                                    children: meal.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 whitespace-nowrap\",\n                                                                    children: [\n                                                                        \"₹\",\n                                                                        meal.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-2 flex-1\",\n                                                            children: meal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"by \",\n                                                                            meal.provider.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        onClick: ()=>addToCart(meal._id),\n                                                                        className: \"bg-orange-500 hover:bg-orange-600 text-white rounded-full px-6 w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Add to Cart\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, meal._id, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this))\n                            }, selectedCategory + searchQuery, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        !loading && filteredMeals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"text-center py-16\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"No meals found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Try adjusting your search or filters\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.section, {\n                className: \"py-20 bg-gradient-to-r from-orange-500 to-red-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-5xl font-bold mb-6\",\n                                children: \"Ready to Start Cooking?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                children: \"Join our community of home chefs and start earning by sharing your delicious homemade meals\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                variants: itemVariants,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-white text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg rounded-full shadow-lg\",\n                                    children: [\n                                        \"Become a Chef Partner\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                children: cartItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"fixed bottom-6 right-6 bg-orange-500 text-white p-4 rounded-full shadow-lg z-50\",\n                    initial: {\n                        scale: 0,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0,\n                        opacity: 0\n                    },\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: cartItems.length\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(MealMatePage, \"QbdZnf/aZuzMtNoYOsvKik/Rrco=\");\n_c = MealMatePage;\nvar _c;\n$RefreshReg$(_c, \"MealMatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});