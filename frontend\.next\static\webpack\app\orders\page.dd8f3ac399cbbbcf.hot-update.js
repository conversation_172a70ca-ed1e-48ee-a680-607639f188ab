"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    var _statusConfig_selectedOrder_status, _statusConfig_selectedOrder_status1, _statusConfig_selectedOrder_status2;\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [selectedOrder, setSelectedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOrderDetails, setShowOrderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Remove auto-refresh - not needed for this implementation\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n                // Refresh orders to get updated data\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n                // Refresh orders to show updated status\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const handleViewDetails = (order)=>{\n        setSelectedOrder(order);\n        setShowOrderDetails(true);\n    };\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Order Management\" : \"My Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Manage orders for your meals\" : \"Track your meal orders\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: fetchOrders,\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 mb-8\",\n                            children: [\n                                \"all\",\n                                \"pending\",\n                                \"confirmed\",\n                                \"preparing\",\n                                \"ready\",\n                                \"delivered\",\n                                \"cancelled\"\n                            ].map((status)=>{\n                                var _statusConfig_status;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: filter === status ? \"default\" : \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setFilter(status),\n                                    className: \"capitalize\",\n                                    children: [\n                                        status === \"all\" ? \"All Orders\" : ((_statusConfig_status = statusConfig[status]) === null || _statusConfig_status === void 0 ? void 0 : _statusConfig_status.text) || status,\n                                        status !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"ml-2\",\n                                            children: orders.filter((order)=>order.status === status).length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, status, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No orders found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: filter === \"all\" ? \"You don't have any orders yet.\" : 'No orders with status \"'.concat(filter, '\".')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this) : filteredOrders.map((order, index)=>{\n                                var _statusConfig_order_status;\n                                const StatusIcon = ((_statusConfig_order_status = statusConfig[order.status]) === null || _statusConfig_order_status === void 0 ? void 0 : _statusConfig_order_status.icon) || _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                                const statusInfo = statusConfig[order.status];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0,\n                                        scale: updatingOrders.has(order._id) ? 0.98 : 1\n                                    },\n                                    transition: {\n                                        delay: index * 0.1,\n                                        scale: {\n                                            duration: 0.2\n                                        }\n                                    },\n                                    className: updatingOrders.has(order._id) ? \"ring-2 ring-orange-300 ring-opacity-50\" : \"\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-2 rounded-full \".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                        className: \"w-4 h-4 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                            className: \"text-lg\",\n                                                                            children: order.meal.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"Order #\",\n                                                                                order._id.slice(-8)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            className: \"\".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\", \" text-white\"),\n                                                            children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text) || order.status\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"Total Amount\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: [\n                                                                                    \"₹\",\n                                                                                    order.meal.price * order.quantity\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"Quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 437,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: order.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"Delivery Date\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: new Date(order.deliveryDate).toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-semibold text-sm\",\n                                                                                children: order.deliveryAddress\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? order.user.name : order.provider.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Order Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: new Date(order.createdAt).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-1\",\n                                                                children: \"Special Instructions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: order.specialInstructions\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: \"Order Progress\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].map((status, idx)=>{\n                                                                    const isActive = [\n                                                                        \"pending\",\n                                                                        \"confirmed\",\n                                                                        \"preparing\",\n                                                                        \"ready\",\n                                                                        \"delivered\"\n                                                                    ].indexOf(order.status) >= idx;\n                                                                    const isCurrent = order.status === status;\n                                                                    const isCompleted = [\n                                                                        \"pending\",\n                                                                        \"confirmed\",\n                                                                        \"preparing\",\n                                                                        \"ready\",\n                                                                        \"delivered\"\n                                                                    ].indexOf(order.status) > idx;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-3 h-3 rounded-full border-2 transition-all duration-300 \".concat(isCompleted ? \"bg-green-500 border-green-500\" : isCurrent ? \"bg-orange-500 border-orange-500 animate-pulse\" : isActive ? \"bg-blue-500 border-blue-500\" : \"bg-gray-200 border-gray-300\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            idx < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-8 h-0.5 transition-all duration-300 \".concat(isCompleted || isActive && idx < [\n                                                                                    \"pending\",\n                                                                                    \"confirmed\",\n                                                                                    \"preparing\",\n                                                                                    \"ready\",\n                                                                                    \"delivered\"\n                                                                                ].indexOf(order.status) ? \"bg-green-500\" : \"bg-gray-200\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, status, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between mt-1\",\n                                                                children: [\n                                                                    \"Pending\",\n                                                                    \"Confirmed\",\n                                                                    \"Preparing\",\n                                                                    \"Ready\",\n                                                                    \"Delivered\"\n                                                                ].map((label, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs transition-all duration-300 \".concat([\n                                                                            \"pending\",\n                                                                            \"confirmed\",\n                                                                            \"preparing\",\n                                                                            \"ready\",\n                                                                            \"delivered\"\n                                                                        ].indexOf(order.status) >= idx ? \"text-gray-700 font-medium\" : \"text-gray-400\"),\n                                                                        children: label\n                                                                    }, label, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            getStatusActions(order),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleViewDetails(order),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"View Details\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 21\n                                    }, this)\n                                }, order._id, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n                open: showOrderDetails,\n                onOpenChange: setShowOrderDetails,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                    className: \"max-w-2xl max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                children: \"Order Details\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: selectedOrder.meal.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Order #\",\n                                                        selectedOrder._id.slice(-8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            className: \"\".concat(((_statusConfig_selectedOrder_status = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status === void 0 ? void 0 : _statusConfig_selectedOrder_status.color) || \"bg-gray-500\", \" text-white\"),\n                                            children: ((_statusConfig_selectedOrder_status1 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status1 === void 0 ? void 0 : _statusConfig_selectedOrder_status1.text) || selectedOrder.status\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Customer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedOrder.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: selectedOrder.user.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Quantity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedOrder.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: [\n                                                                \"₹\",\n                                                                selectedOrder.meal.price * selectedOrder.quantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Delivery Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.deliveryDate).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Order Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: new Date(selectedOrder.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: (_statusConfig_selectedOrder_status2 = statusConfig[selectedOrder.status]) === null || _statusConfig_selectedOrder_status2 === void 0 ? void 0 : _statusConfig_selectedOrder_status2.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Delivery Address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.deliveryAddress\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                selectedOrder.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Special Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1 p-3 bg-gray-50 rounded-lg\",\n                                            children: selectedOrder.specialInstructions\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 pt-4 border-t\",\n                                    children: [\n                                        getStatusActions(selectedOrder),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowOrderDetails(false),\n                                            className: \"ml-auto\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 567,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"/rsJJBa7zh4RQKMamCNqHzRuT48=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});