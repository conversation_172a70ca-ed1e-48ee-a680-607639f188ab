"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./app/orders/page.tsx":
/*!*****************************!*\
  !*** ./app/orders/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrdersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChefHat,Clock,DollarSign,Eye,MapPin,Package,RefreshCw,Truck,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst statusConfig = {\n    pending: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"bg-yellow-500\",\n        text: \"Pending\",\n        description: \"Waiting for confirmation\"\n    },\n    confirmed: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"bg-blue-500\",\n        text: \"Confirmed\",\n        description: \"Order confirmed by chef\"\n    },\n    preparing: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"bg-purple-500\",\n        text: \"Preparing\",\n        description: \"Your meal is being prepared\"\n    },\n    ready: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        color: \"bg-orange-500\",\n        text: \"Ready\",\n        description: \"Ready for pickup/delivery\"\n    },\n    delivered: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: \"bg-green-500\",\n        text: \"Delivered\",\n        description: \"Order completed\"\n    },\n    cancelled: {\n        icon: _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: \"bg-red-500\",\n        text: \"Cancelled\",\n        description: \"Order cancelled\"\n    }\n};\nfunction OrdersPage() {\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Remove auto-refresh - not needed for this implementation\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n            } else {\n                throw new Error(\"Failed to fetch orders\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, newStatus)=>{\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                var _statusConfig_newStatus;\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order status changed to \".concat(((_statusConfig_newStatus = statusConfig[newStatus]) === null || _statusConfig_newStatus === void 0 ? void 0 : _statusConfig_newStatus.text) || newStatus, \".\")\n                });\n                // Refresh orders to get updated data\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const cancelOrder = async (orderId)=>{\n        if (!confirm(\"Are you sure you want to cancel this order?\")) {\n            return;\n        }\n        // Optimistically update the UI first\n        setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                    ...order,\n                    status: \"cancelled\"\n                } : order));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                // Remove the order from the list or mark as cancelled\n                const data = await response.json();\n                if (data.data && Object.keys(data.data).length === 0) {\n                    // Order was deleted, remove from list\n                    setOrders((prevOrders)=>prevOrders.filter((order)=>order._id !== orderId));\n                } else {\n                    // Order was marked as cancelled\n                    setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                                ...order,\n                                status: \"cancelled\"\n                            } : order));\n                }\n                toast({\n                    title: \"Order cancelled\",\n                    description: \"Your order has been cancelled successfully.\"\n                });\n            } else {\n                // Revert the optimistic update on error\n                fetchOrders();\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to cancel order\");\n            }\n        } catch (error) {\n            // Revert the optimistic update on error\n            fetchOrders();\n            toast({\n                title: \"Cancellation failed\",\n                description: error instanceof Error ? error.message : \"Failed to cancel order.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const filteredOrders = orders.filter((order)=>{\n        if (filter === \"all\") return true;\n        return order.status === filter;\n    });\n    const getStatusActions = (order)=>{\n        const actions = [];\n        const isUpdating = updatingOrders.has(order._id);\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"provider\" && order.provider._id === user._id) {\n            // Provider actions\n            if (order.status === \"pending\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Confirm\"\n                }, \"confirm\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"confirmed\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                    className: \"bg-purple-500 hover:bg-purple-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Start Preparing\"\n                }, \"preparing\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"preparing\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                    className: \"bg-orange-500 hover:bg-orange-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Ready\"\n                }, \"ready\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, this));\n            }\n            if (order.status === \"ready\") {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Updating...\" : \"Mark Delivered\"\n                }, \"delivered\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        // Customer actions\n        if ((user === null || user === void 0 ? void 0 : user.role) === \"customer\" && order.user._id === user._id) {\n            if ([\n                \"pending\",\n                \"confirmed\"\n            ].includes(order.status)) {\n                actions.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    size: \"sm\",\n                    variant: \"destructive\",\n                    onClick: ()=>cancelOrder(order._id),\n                    disabled: isUpdating,\n                    children: isUpdating ? \"Cancelling...\" : \"Cancel Order\"\n                }, \"cancel\", false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 11\n                }, this));\n            }\n        }\n        return actions;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 316,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Order Management\" : \"My Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Manage orders for your meals\" : \"Track your meal orders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: fetchOrders,\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Auto-updating every 20s\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mb-8\",\n                        children: [\n                            \"all\",\n                            \"pending\",\n                            \"confirmed\",\n                            \"preparing\",\n                            \"ready\",\n                            \"delivered\",\n                            \"cancelled\"\n                        ].map((status)=>{\n                            var _statusConfig_status;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: filter === status ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setFilter(status),\n                                className: \"capitalize\",\n                                children: [\n                                    status === \"all\" ? \"All Orders\" : ((_statusConfig_status = statusConfig[status]) === null || _statusConfig_status === void 0 ? void 0 : _statusConfig_status.text) || status,\n                                    status !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: orders.filter((order)=>order.status === status).length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, status, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"No orders found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: filter === \"all\" ? \"You don't have any orders yet.\" : 'No orders with status \"'.concat(filter, '\".')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 15\n                        }, this) : filteredOrders.map((order, index)=>{\n                            var _statusConfig_order_status;\n                            const StatusIcon = ((_statusConfig_order_status = statusConfig[order.status]) === null || _statusConfig_order_status === void 0 ? void 0 : _statusConfig_order_status.icon) || _barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                            const statusInfo = statusConfig[order.status];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    scale: updatingOrders.has(order._id) ? 0.98 : 1\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    scale: {\n                                        duration: 0.2\n                                    }\n                                },\n                                className: updatingOrders.has(order._id) ? \"ring-2 ring-orange-300 ring-opacity-50\" : \"\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-full \".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-lg\",\n                                                                        children: order.meal.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Order #\",\n                                                                            order._id.slice(-8)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"\".concat((statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || \"bg-gray-500\", \" text-white\"),\n                                                        children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.text) || order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Total Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"₹\",\n                                                                                order.meal.price * order.quantity\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Quantity\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: order.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Delivery Date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: new Date(order.deliveryDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Address\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold text-sm\",\n                                                                            children: order.deliveryAddress\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? \"Customer\" : \"Chef\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"provider\" ? order.user.name : order.provider.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Order Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: new Date(order.createdAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 25\n                                                }, this),\n                                                order.specialInstructions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                            children: \"Special Instructions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: order.specialInstructions\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Order Progress\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                \"pending\",\n                                                                \"confirmed\",\n                                                                \"preparing\",\n                                                                \"ready\",\n                                                                \"delivered\"\n                                                            ].map((status, idx)=>{\n                                                                const isActive = [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) >= idx;\n                                                                const isCurrent = order.status === status;\n                                                                const isCompleted = [\n                                                                    \"pending\",\n                                                                    \"confirmed\",\n                                                                    \"preparing\",\n                                                                    \"ready\",\n                                                                    \"delivered\"\n                                                                ].indexOf(order.status) > idx;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full border-2 transition-all duration-300 \".concat(isCompleted ? \"bg-green-500 border-green-500\" : isCurrent ? \"bg-orange-500 border-orange-500 animate-pulse\" : isActive ? \"bg-blue-500 border-blue-500\" : \"bg-gray-200 border-gray-300\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        idx < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-0.5 transition-all duration-300 \".concat(isCompleted || isActive && idx < [\n                                                                                \"pending\",\n                                                                                \"confirmed\",\n                                                                                \"preparing\",\n                                                                                \"ready\",\n                                                                                \"delivered\"\n                                                                            ].indexOf(order.status) ? \"bg-green-500\" : \"bg-gray-200\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, status, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between mt-1\",\n                                                            children: [\n                                                                \"Pending\",\n                                                                \"Confirmed\",\n                                                                \"Preparing\",\n                                                                \"Ready\",\n                                                                \"Delivered\"\n                                                            ].map((label, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs transition-all duration-300 \".concat([\n                                                                        \"pending\",\n                                                                        \"confirmed\",\n                                                                        \"preparing\",\n                                                                        \"ready\",\n                                                                        \"delivered\"\n                                                                    ].indexOf(order.status) >= idx ? \"text-gray-700 font-medium\" : \"text-gray-400\"),\n                                                                    children: label\n                                                                }, label, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        getStatusActions(order),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChefHat_Clock_DollarSign_Eye_MapPin_Package_RefreshCw_Truck_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"View Details\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 21\n                                }, this)\n                            }, order._id, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 19\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 324,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"Ee5hu5nRR4F/G/rr3uKxu0BVunI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/orders/page.tsx\n"));

/***/ })

});