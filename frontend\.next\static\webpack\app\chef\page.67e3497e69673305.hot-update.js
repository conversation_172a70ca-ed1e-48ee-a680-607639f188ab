"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chef/page",{

/***/ "(app-pages-browser)/./app/chef/page.tsx":
/*!***************************!*\
  !*** ./app/chef/page.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChefDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChefHat,DollarSign,Edit,Eye,Package,Plus,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst statusColors = {\n    pending: \"bg-yellow-100 text-yellow-800\",\n    confirmed: \"bg-blue-100 text-blue-800\",\n    preparing: \"bg-orange-100 text-orange-800\",\n    ready: \"bg-green-100 text-green-800\",\n    delivered: \"bg-gray-100 text-gray-800\",\n    cancelled: \"bg-red-100 text-red-800\"\n};\nfunction ChefDashboard() {\n    _s();\n    const { user, token } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [meals, setMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [updatingOrders, setUpdatingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMeals: 0,\n        totalOrders: 0,\n        totalEarnings: 0,\n        averageRating: 4.8\n    });\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && token) {\n            fetchMeals();\n            fetchOrders();\n        }\n    }, [\n        user,\n        token\n    ]);\n    // Remove auto-refresh - not needed\n    const fetchMeals = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/meals\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                // Filter meals by current user (provider)\n                const userMeals = data.data.filter((meal)=>meal.user === (user === null || user === void 0 ? void 0 : user._id));\n                setMeals(userMeals);\n                setStats((prev)=>({\n                        ...prev,\n                        totalMeals: userMeals.length\n                    }));\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch meals. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchOrders = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders\"), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOrders(data.data);\n                // Calculate earnings\n                const totalEarnings = data.data.filter((order)=>order.status === \"delivered\").reduce((sum, order)=>sum + order.meal.price * order.quantity, 0);\n                setStats((prev)=>({\n                        ...prev,\n                        totalOrders: data.data.length,\n                        totalEarnings\n                    }));\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch orders. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateOrderStatus = async (orderId, status)=>{\n        // Add to updating set for loading state\n        setUpdatingOrders((prev)=>new Set(prev).add(orderId));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/orders/\").concat(orderId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (response.ok) {\n                // Update the order in the local state immediately\n                setOrders((prevOrders)=>prevOrders.map((order)=>order._id === orderId ? {\n                            ...order,\n                            status: status\n                        } : order));\n                toast({\n                    title: \"Order updated\",\n                    description: \"Order \".concat(status === \"confirmed\" ? \"accepted\" : status === \"cancelled\" ? \"declined\" : \"updated\")\n                });\n                // Refresh orders to get any additional updates\n                fetchOrders();\n            } else {\n                const error = await response.json();\n                throw new Error(error.error || \"Failed to update order\");\n            }\n        } catch (error) {\n            toast({\n                title: \"Update failed\",\n                description: error instanceof Error ? error.message : \"Failed to update order status.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            // Remove from updating set\n            setUpdatingOrders((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(orderId);\n                return newSet;\n            });\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Chef Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your meals and orders\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Meals\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: stats.totalMeals\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8 text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: stats.totalOrders\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Total Earnings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: [\n                                                                \"₹\",\n                                                                stats.totalEarnings\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Average Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: stats.averageRating\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-8 h-8 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                children: \"My Meals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                children: \"Manage your meal listings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add Meal\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: meals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No meals yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: \"Start by adding your first meal!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        children: \"Add Your First Meal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: meals.slice(0, 3).map((meal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.1 * index\n                                                        },\n                                                        className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: meal.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 line-clamp-1\",\n                                                                        children: meal.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                children: meal.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-600\",\n                                                                                children: [\n                                                                                    \"₹\",\n                                                                                    meal.price\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                                lineNumber: 309,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, meal._id, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.6\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    children: \"Recent Orders\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: \"Manage incoming orders\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: orders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChefHat_DollarSign_Edit_Eye_Package_Plus_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No orders yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Orders will appear here when customers place them\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: orders.slice(0, 3).map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0,\n                                                            scale: updatingOrders.has(order._id) ? 0.98 : 1\n                                                        },\n                                                        transition: {\n                                                            delay: 0.1 * index,\n                                                            scale: {\n                                                                duration: 0.2\n                                                            }\n                                                        },\n                                                        className: \"p-4 border rounded-lg hover:shadow-md transition-all \".concat(updatingOrders.has(order._id) ? \"border-orange-300 bg-orange-50 ring-2 ring-orange-200\" : \"border-gray-200\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: order.meal.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        className: statusColors[order.status],\n                                                                        children: order.status.charAt(0).toUpperCase() + order.status.slice(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: [\n                                                                    \"Customer: \",\n                                                                    order.user.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Qty: \",\n                                                                            order.quantity,\n                                                                            \" • ₹\",\n                                                                            order.meal.price * order.quantity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    order.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                size: \"sm\",\n                                                                                onClick: ()=>updateOrderStatus(order._id, \"confirmed\"),\n                                                                                disabled: updatingOrders.has(order._id),\n                                                                                className: \"bg-green-600 hover:bg-green-700\",\n                                                                                children: updatingOrders.has(order._id) ? \"Accepting...\" : \"Accept\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                                lineNumber: 379,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                size: \"sm\",\n                                                                                variant: \"outline\",\n                                                                                onClick: ()=>updateOrderStatus(order._id, \"cancelled\"),\n                                                                                disabled: updatingOrders.has(order._id),\n                                                                                className: \"text-red-600 border-red-600 hover:bg-red-50\",\n                                                                                children: updatingOrders.has(order._id) ? \"Declining...\" : \"Decline\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    order.status === \"confirmed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>updateOrderStatus(order._id, \"preparing\"),\n                                                                        disabled: updatingOrders.has(order._id),\n                                                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                                                        children: updatingOrders.has(order._id) ? \"Starting...\" : \"Start Preparing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    order.status === \"preparing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>updateOrderStatus(order._id, \"ready\"),\n                                                                        disabled: updatingOrders.has(order._id),\n                                                                        className: \"bg-orange-600 hover:bg-orange-700\",\n                                                                        children: updatingOrders.has(order._id) ? \"Marking...\" : \"Mark Ready\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    order.status === \"ready\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>updateOrderStatus(order._id, \"delivered\"),\n                                                                        disabled: updatingOrders.has(order._id),\n                                                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                        children: updatingOrders.has(order._id) ? \"Delivering...\" : \"Mark Delivered\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, order._id, true, {\n                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\chef\\\\page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(ChefDashboard, \"T/T6yGvI+qhL4ZdWxbVD5ql02+k=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = ChefDashboard;\nvar _c;\n$RefreshReg$(_c, \"ChefDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chef/page.tsx\n"));

/***/ })

});